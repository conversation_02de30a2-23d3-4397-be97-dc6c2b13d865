# NeoRevv Cross-Platform UI

[![Flutter](https://img.shields.io/badge/Flutter-3.8.1+-02569B?style=flat&logo=flutter&logoColor=white)](https://flutter.dev)
[![Dart](https://img.shields.io/badge/Dart-3.8.1+-0175C2?style=flat&logo=dart&logoColor=white)](https://dart.dev)
[![License](https://img.shields.io/badge/License-Private-red)](LICENSE)

A powerful cross-platform real estate management application built with Flutter, designed to streamline agent management, commission tracking, and brokerage operations across Web, Android, and iOS platforms.

## 🏢 About NeoRevv

NeoRevv is a comprehensive real estate management platform that empowers brokerages and agents with tools to:

- **Manage Agent Networks**: Hierarchical agent management with drill-down navigation
- **Track Commissions**: Real-time commission tracking and performance analytics
- **Monitor Sales**: Sales review documentation and performance metrics
- **Dashboard Analytics**: Interactive charts and performance indicators
- **Multi-Role Support**: Platform owners, brokerages, and agents with role-based access

## ✨ Key Features

### 🎯 Core Functionality
- **Multi-Platform Support**: Native performance on Web, iOS, and Android
- **Role-Based Access Control**: Platform Owner, Platform Admin, Brokerage, and Agent roles
- **Real-Time Dashboard**: Interactive charts with FL Chart integration
- **Agent Network Visualization**: Hierarchical agent network with expandable views
- **Commission Management**: Comprehensive commission tracking and analytics
- **Document Management**: Sales review documents with file picker integration
- **Responsive Design**: Adaptive UI for desktop, tablet, and mobile devices

### 🔐 Security & Authentication
- **Secure Storage**: Flutter Secure Storage for sensitive data
- **OAuth Integration**: Gmail and Apple Sign-In support
- **Session Management**: Persistent authentication with Hydrated Bloc
- **API Security**: Secure API communication with Dio HTTP client

### 📊 Data Management
- **State Management**: BLoC pattern with Cubit for predictable state management
- **Offline Support**: Hydrated Bloc for offline data persistence
- **Caching**: Cached network images for optimal performance
- **Real-Time Updates**: Live data synchronization across the platform

## 🏗️ Architecture

The application follows **Clean Architecture** principles with clear separation of concerns:

```
lib/
├── main.dart                 # Application entry point
├── main_layout_screen.dart   # Main layout wrapper
└── src/
    ├── core/                 # Core utilities and configurations
    │   ├── config/          # App constants and configurations
    │   ├── enum/            # Application enumerations
    │   ├── navigation/      # Go Router navigation setup
    │   ├── network/         # API client and network utilities
    │   ├── services/        # Dependency injection and services
    │   ├── theme/           # App theming and styling
    │   └── utils/           # Utility functions
    ├── data/                # Data layer
    │   └── repository/      # Repository implementations
    ├── domain/              # Business logic layer
    │   ├── models/          # Data models
    │   └── repository/      # Repository interfaces
    └── presentation/        # UI layer
        ├── cubit/           # State management (BLoC/Cubit)
        ├── screens/         # Application screens
        └── shared/          # Shared UI components
```

## 🚀 Getting Started

### Prerequisites

- **Flutter SDK**: 3.8.1 or higher
- **Dart SDK**: 3.8.1 or higher
- **IDE**: VS Code, Android Studio, or IntelliJ IDEA
- **Platform-specific requirements**:
  - **Android**: Android Studio, Android SDK
  - **iOS**: Xcode (macOS only)
  - **Web**: Chrome browser

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/neorevv/neorevv-cross-platform-ui.git
   cd neorevv-cross-platform-ui
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Run the application**
   ```bash
   # Web
   flutter run -d chrome

   # Android
   flutter run -d android

   # iOS (macOS only)
   flutter run -d ios
   ```

## 📱 Supported Platforms

| Platform | Status | Notes |
|----------|--------|-------|
| 🌐 Web | ✅ Supported | Primary platform with full feature set |
| 🤖 Android | ✅ Supported | Native Android experience |
| 🍎 iOS | ✅ Supported | Native iOS experience |

## 🛠️ Tech Stack

### Core Framework
- **Flutter**: Cross-platform UI framework
- **Dart**: Programming language

### State Management
- **Flutter BLoC**: Predictable state management
- **Hydrated BLoC**: Persistent state management
- **Flutter Hooks**: React-like hooks for Flutter

### Navigation
- **Go Router**: Declarative routing solution

### Networking & Data
- **Dio**: HTTP client for API communication
- **Flutter Secure Storage**: Secure local storage
- **Cached Network Image**: Optimized image loading and caching

### UI & Design
- **Google Fonts**: Custom typography (Poppins font family)
- **FL Chart**: Interactive charts and graphs
- **Auto Size Text**: Responsive text sizing
- **Signature**: Digital signature capture

### Utilities
- **Get It**: Dependency injection
- **Path Provider**: File system path access
- **File Picker**: File selection functionality
- **Image Picker**: Camera and gallery image selection
- **Intl**: Internationalization support

## 🎨 Design System

### Typography
- **Primary Font**: Poppins (Complete font family with weights 100-900)
- **Responsive Text**: Auto-sizing text components

### Color Scheme
- **Primary Color**: Defined in `AppTheme.primaryColor`
- **Background**: Custom scaffold background color
- **Adaptive**: Light theme with seed color generation

### Responsive Design
- **Breakpoints**: Mobile, Tablet, Desktop responsive breakpoints
- **Adaptive Layouts**: Context-aware UI components
- **Cross-Platform**: Consistent experience across all platforms

## 📂 Key Directories

### Core (`lib/src/core/`)
- **config/**: Application constants, strings, and configuration
- **navigation/**: Go Router setup and route definitions
- **services/**: Dependency injection and service locators
- **theme/**: Application theming and styling

### Presentation (`lib/src/presentation/`)
- **screens/**: All application screens organized by feature
  - `dashboard/`: Main dashboard with analytics
  - `auth/`: Authentication screens
  - `agent/`: Agent management screens
  - `broker/`: Brokerage management screens
  - `sales/`: Sales and commission screens
  - `agent_network/`: Agent network visualization
- **cubit/**: State management for each feature
- **shared/**: Reusable UI components

### Domain (`lib/src/domain/`)
- **models/**: Data models and entities
- **repository/**: Repository interfaces (contracts)

### Data (`lib/src/data/`)
- **repository/**: Repository implementations with API integration

## 🔧 Development

### Code Style
- Follow [Dart Style Guide](https://dart.dev/guides/language/effective-dart/style)
- Use `flutter_lints` for code analysis
- Maintain consistent naming conventions

### State Management Pattern
```dart
// Example Cubit usage
class ExampleCubit extends Cubit<ExampleState> {
  ExampleCubit(this._repository) : super(ExampleInitial());

  final ExampleRepository _repository;

  Future<void> loadData() async {
    emit(ExampleLoading());
    try {
      final data = await _repository.getData();
      emit(ExampleLoaded(data));
    } catch (e) {
      emit(ExampleError(e.toString()));
    }
  }
}
```

### Dependency Injection
```dart
// Register dependencies in locator.dart
void setupLocator() {
  locator.registerLazySingleton<ExampleRepository>(
    () => ExampleRepositoryImpl(),
  );
}
```

## 📦 Build & Deployment

### Web Deployment
```bash
flutter build web --release
```

### Android APK
```bash
flutter build apk --release
```

### iOS App Store
```bash
flutter build ios --release
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feat-amazing-feature`)
3. Commit your changes (`git commit -m 'add amazing feature[#issue-number]'`)
4. Push to the branch (`git push origin feat-amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is private and proprietary. All rights reserved.
