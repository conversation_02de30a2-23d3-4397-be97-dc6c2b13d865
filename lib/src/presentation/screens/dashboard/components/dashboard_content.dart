import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:neorevv/src/domain/models/agent_model.dart';
import '/src/domain/models/broker_api.dart';
import '/src/presentation/screens/agent/components/agents_table.dart';
import 'package:neorevv/src/core/enum/user_role.dart';
import 'package:neorevv/src/presentation/cubit/infocard/cubit/infocard_cubit.dart';
import 'package:neorevv/src/presentation/cubit/infocard/cubit/infocard_state.dart';
import 'package:neorevv/src/presentation/cubit/user/user_cubit.dart';
import 'package:neorevv/src/presentation/screens/agent/components/agents_table.dart';
import '../../../../core/config/app_strings.dart';
import '../../../../core/enum/user_role.dart';
import '../../../../domain/models/broker.dart';
import '../../../../core/config/constants.dart';
import '../../../../core/config/responsive.dart';
import '../../../../domain/models/info_card.dart';
import '../../../cubit/broker/broker_cubit.dart';
import '../../../cubit/user/user_cubit.dart';
import '../../agent/agents_list_screen.dart';
import 'info_card_widget.dart';
import 'brokers_table.dart';
import 'commission_card.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../core/config/json_consts.dart';

class DashboardContent extends HookWidget {
  final Function(Brokers)? onNavigateToAgentNetwork;
  final Function(AgentModel)? onNavigateToAgentNetworkAgent;

  DashboardContent({
    Key? key,
    this.onNavigateToAgentNetwork,
    this.onNavigateToAgentNetworkAgent,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final user = context.watch<UserCubit>().state.user;
    useEffect(() {
      context.read<DashboardCubit>().fetchAllDashboardInfoCardsitems(
        user?.role ?? UserRole.none,
      );
    });

    final size = MediaQuery.of(context).size;
    return Responsive(
      mobile: _buildDashboardContent(context),
      tablet: _buildDashboardContent(context),
      desktop: Container(
        constraints: BoxConstraints(
          minHeight: size.height,
          maxHeight: size.height,
        ),
        child: _buildDashboardContent(context),
      ),
    );
  }

  Widget _buildDashboardContent(BuildContext context) {
    final isDesktop = Responsive.isDesktop(context);
    final isTablet = Responsive.isTablet(context);
    final isMobile = Responsive.isMobile(context);
    //user
    final user = context.watch<UserCubit>().state.user;
    final UserRole role = user?.role ?? UserRole.none;
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          flex: 5,
          child: Column(
            children: [
              infocardWidget(context),
              SizedBox(height: defaultPadding),
              _buildTableView(context, role),
              if (Responsive.isMobile(context))
                SizedBox(height: defaultPadding),

              if (isMobile || isTablet) ...[
                SizedBox(height: defaultPadding),
                CommissionCard(),
              ],
            ],
          ),
        ),
        if (!isMobile && !isTablet) SizedBox(width: defaultPadding),

        if (isDesktop) ...[CommissionCard()],
      ],
    );
  }

  Widget infocardWidget(BuildContext context) {
    final Size size = MediaQuery.of(context).size;
    return Responsive(
      mobile: InfoCardGridView(
        crossAxisCount: 2,
        childAspectRatio: size.width < 700 && size.width > 350 ? 1.8 : 2.5,
      ),
      tablet: InfoCardGridView(
        crossAxisCount: 2,
        childAspectRatio: size.width < tabletBreakpoint && size.width > 700
            ? 3
            : 2.2,
      ),
      desktop: InfoCardGridView(
        childAspectRatio: size.width < desktopBreakpoint ? 1.8 : 2.2,
      ),
    );
  }

  Widget _buildTableView(BuildContext context, UserRole role) {
    final isDesktop = Responsive.isDesktop(context);
    switch (role) {
      case UserRole.platformOwner:
        return _buildBrokersTableView(isDesktop, false);
      case UserRole.admin:
        return _buildBrokersTableView(isDesktop, true);
      case UserRole.brokerage:
        return _buildAgentsTableView(isDesktop, true);
      case UserRole.officeStaff:
        return _buildAgentsTableView(isDesktop, false);
      case UserRole.agent:
        return _buildRecruitedAgentsTableView(isDesktop);
      default:
        return _buildBrokersTableView(isDesktop, false);
    }
  }

  Widget _buildBrokersTableView(bool isDesktop, bool isAdmin) {
    return isDesktop
        ? Flexible(
            // Changed from Expanded to Flexible
            child: BrokersTable(
              onNavigateToAgentNetwork: onNavigateToAgentNetwork,
              showEditOptions: isAdmin,
            ),
          )
        : LayoutBuilder(
            builder: (context, constraints) {
              final size = MediaQuery.of(context).size;
              return Container(
                height: size.height < 500
                    ? size.height * 1.5
                    : size.height < 700
                    ? size.height
                    : size.height / 1.18,
                child: BrokersTable(
                  onNavigateToAgentNetwork: onNavigateToAgentNetwork,
                  showEditOptions: isAdmin,
                ),
              );
            },
          );
  }

  Widget _buildAgentsTableView(bool isDesktop, bool isBroker) {
    return isDesktop
        ? Flexible(
            child: AgentsTable(
              showEditOptions: isBroker,
              onNavigateToAgentNetworkAgent: onNavigateToAgentNetworkAgent,
            ),
          )
        : AgentsTable(
            showEditOptions: isBroker,
            onNavigateToAgentNetworkAgent: onNavigateToAgentNetworkAgent,
          );
  }

  Widget _buildRecruitedAgentsTableView(bool isDesktop) {
    return isDesktop
        ? Flexible(
            child: AgentsTable(
              showEditOptions: false,
              showRecruits: true,
              onNavigateToAgentNetworkAgent: onNavigateToAgentNetworkAgent,
            ),
          )
        : AgentsTable(
            showEditOptions: false,
            showRecruits: true,
            onNavigateToAgentNetworkAgent: onNavigateToAgentNetworkAgent,
          );
  }
}

class InfoCardGridView extends StatelessWidget {
  const InfoCardGridView({
    Key? key,
    this.crossAxisCount = 4,
    this.childAspectRatio = 1,
  }) : super(key: key);

  final int crossAxisCount;
  final double childAspectRatio;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<DashboardCubit, DashboardCubitState>(
      builder: (context, state) {
        if (state is DashboardCardsLoading) {
          return Center(child: CircularProgressIndicator());
        } else if (state is DashboardCardsSuccess) {
          final infoCards = state.infocards;
          return GridView.builder(
            physics: NeverScrollableScrollPhysics(),
            shrinkWrap: true,
            itemCount: infoCards.length,
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: crossAxisCount,
              crossAxisSpacing: defaultPadding,
              mainAxisSpacing: defaultPadding,
              childAspectRatio: childAspectRatio,
            ),
            itemBuilder: (context, index) => InfoCardWidget(
              title: infoCards[index].title,
              value: infoCards[index].value,
              icon: infoCards[index].assetImage,
              iconColor: infoCards[index].iconColor,
              subtitle: infoCards[index].subtitle,
              additionalInfo: infoCards[index].additionalInfo,
            ),
          );
        } else if (state is DashboardCardsFailure) {
          return Center(child: Text('Error: ${state.error}'));
        } else {
          return Center(child: Text('No data available'));
        }
      },
    );
  }
}

class Footer extends StatelessWidget {
  const Footer({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bool isMobile = Responsive.isMobile(context);
    final bool isSmallMobile = Responsive.isSmallMobile(context);

    return Container(
      padding: EdgeInsets.symmetric(
        vertical: isMobile ? defaultPadding / 2 : defaultPadding * 2,
        horizontal: defaultPadding,
      ),
      child: isSmallMobile
          ? _buildMobileFooter(context)
          : _buildDesktopFooter(context, isMobile),
    );
  }

  Widget _buildDesktopFooter(BuildContext context, bool isMobile) {
    final double spacing = isMobile ? 2.0 : 4.0;
    final double fontSize = isMobile ? 10.0 : 12.0;

    // Always use Wrap to prevent overflow
    return Wrap(
      alignment: WrapAlignment.center,
      crossAxisAlignment: WrapCrossAlignment.center,
      spacing: spacing,
      runSpacing: 4.0,
      children: [
        Text(
          copyright,
          style: Theme.of(
            context,
          ).textTheme.bodySmall?.copyWith(fontSize: fontSize),
          textAlign: TextAlign.center,
        ),
        _buildFooterButton(homeFooterLabel, fontSize),
        _buildFooterButton(privacyPolicy, fontSize),
        _buildFooterButton(termsAndConditions, fontSize),
      ],
    );
  }

  Widget _buildFooterButton(String text, double fontSize) {
    return TextButton(
      onPressed: () {},
      style: TextButton.styleFrom(
        padding: EdgeInsets.symmetric(horizontal: 4, vertical: 2),
        minimumSize: Size.zero,
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: fontSize,
          color: AppTheme.primaryTextColor.withValues(alpha: 0.5),
        ),
        overflow: TextOverflow.ellipsis,
        maxLines: 1,
      ),
    );
  }

  Widget _buildMobileFooter(BuildContext context) {
    return Column(
      children: [
        Text(
          copyright,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(fontSize: 10),
          textAlign: TextAlign.center,
          overflow: TextOverflow.ellipsis,
          maxLines: 2,
        ),
        SizedBox(height: 8),
        Wrap(
          alignment: WrapAlignment.center,
          spacing: 4,
          runSpacing: 4,
          children: [
            _buildFooterButton(homeFooterLabel, 10),
            _buildFooterButton(privacyPolicy, 10),
            _buildFooterButton(termsAndConditions, 10),
          ],
        ),
      ],
    );
  }
}
