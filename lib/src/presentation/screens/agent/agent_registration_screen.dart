import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:file_picker/file_picker.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import '../../../core/network/api_consts.dart';
import '/src/presentation/cubit/agent/agent_cubit.dart';
import '/src/presentation/cubit/broker/broker_cubit.dart';
import '/src/presentation/cubit/user/user_cubit.dart';
import '../../../core/config/app_strings.dart';
import '../../shared/components/app_textfield.dart';
import '../../../core/utils/validators.dart';
import '../../../core/utils/dotted_line_painter.dart';
import '../../shared/components/elevated_button.dart';
import '../../../core/config/app_strings.dart' as AppStrings;
import '../../../core/config/constants.dart';
import '../../../core/config/responsive.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/theme/app_fonts.dart';

class AgentRegistrationScreen extends HookWidget {
  AgentRegistrationScreen({super.key});

  final TextEditingController firstNameController = TextEditingController();
  final TextEditingController lastNameController = TextEditingController();
  final TextEditingController phoneController = TextEditingController();
  final TextEditingController emailController = TextEditingController();
  final TextEditingController cityController = TextEditingController();
  final TextEditingController stateController = TextEditingController();
  final TextEditingController postalCodeController = TextEditingController();
  final TextEditingController countryController = TextEditingController();
  final TextEditingController agentLicenseIdController =
      TextEditingController();
  final TextEditingController additionalInfoController =
      TextEditingController();

  // Controllers for invite field
  final TextEditingController firstNameControllerInvite =
      TextEditingController();
  final TextEditingController lastNameControllerInvite =
      TextEditingController();
  final TextEditingController phoneControllerInvite = TextEditingController();
  final TextEditingController emailControllerInvite = TextEditingController();

  final selectedIndex = ValueNotifier(0);
  final _formKey = GlobalKey<FormState>();

  final ValueNotifier<PlatformFile?> agentLicenseFile = ValueNotifier(null);
  final ValueNotifier<bool> showFileUploadError = ValueNotifier(false);

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isMobile = Responsive.isMobile(context);

    return _formWidget(context, size, isMobile);
  }

  Widget _formWidget(BuildContext context, Size size, bool isMobile) {
    return ValueListenableBuilder(
      valueListenable: selectedIndex,
      builder: (context, value, child) {
        return BlocConsumer<BrokerCubit, BrokerState>(
          listener: (context, state) {},
          builder: (context, state) {
            if (state is BrokerLoading) {}
            return Container(
              decoration: BoxDecoration(color: AppTheme.scaffoldBgColor),
              child: Center(
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    Container(
                      constraints: BoxConstraints(
                        maxWidth: isMobile ? double.infinity : 600,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(25),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.1),
                            blurRadius: 10,
                            offset: Offset(0, 5),
                          ),
                        ],
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          _formHeader(context),
                          _formContent(isMobile, context),
                        ],
                      ),
                    ),

                    state is AgentLoading
                        ? Container(
                            color: Colors.amber,
                            width: double.infinity,

                            child: Center(child: CircularProgressIndicator()),
                          )
                        : const SizedBox(),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  Container _formHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: defaultPadding * 2,
        vertical: defaultPadding * 1.5,
      ),
      decoration: BoxDecoration(
        color: AppTheme.roundIconColor,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(25),
          topRight: Radius.circular(25),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            AppStrings.registerAgent,
            style: AppFonts.semiBoldTextStyle(20, color: Colors.white),
          ),
          const SizedBox(height: defaultPadding * 1.5),
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(50),
              color: Colors.white,
            ),
            padding: const EdgeInsets.all(4),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                _registerTab(
                  context,
                  AppStrings.register,
                  selectedIndex.value == 0,
                  0,
                ),
                _registerTab(
                  context,
                  AppStrings.inviteAgent,
                  selectedIndex.value == 1,
                  1,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _registerTab(
    BuildContext context,
    String label,
    bool isActive,
    int index,
  ) {
    return Expanded(
      child: Container(
        margin: EdgeInsets.all(2),
        child: AppButton(
          label: label,
          backgroundColor: isActive ? AppTheme.roundIconColor : Colors.white,
          foregroundColor: Colors.white,
          elevation: 0,
          borderSide: BorderSide.none,
          borderRadius: 50,
          padding: EdgeInsets.symmetric(vertical: 12),
          textStyle: AppFonts.mediumTextStyle(
            14,
            color: isActive ? Colors.white : AppTheme.roundIconColor,
          ),
          onPressed: () {
            _clearForm();
            selectedIndex.value = index;
          },
        ),
      ),
    );
  }

  Container _formContent(bool isMobile, BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(defaultPadding * 2),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(25),
          bottomRight: Radius.circular(25),
        ),
      ),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: Text(
                AppStrings.agentInformation,
                style: AppFonts.semiBoldTextStyle(
                  18,
                  color: AppTheme.primaryTextColor,
                ),
              ),
            ),
            SizedBox(height: defaultPadding * 1.5),

            _buildFormFields(isMobile),

            SizedBox(height: defaultPadding * 2),
            _actionButtons(context),
          ],
        ),
      ),
    );
  }

  Widget _buildFormFields(bool isMobile) {
    bool isRegisterTab = selectedIndex.value == 0;

    if (isMobile) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildFormLabel(AppStrings.firstName, isMandatory: true),
          const SizedBox(height: 8),
          _buildTextFormField(
            isRegisterTab ? firstNameController : firstNameControllerInvite,
            AppStrings.enterFirstName,
            isMandatory: true,
          ),
          const SizedBox(height: defaultPadding),

          _buildFormLabel(AppStrings.lastName, isMandatory: true),
          const SizedBox(height: 8),
          _buildTextFormField(
            isRegisterTab ? lastNameController : lastNameControllerInvite,
            AppStrings.enterLastName,
            isMandatory: true,
          ),
          const SizedBox(height: defaultPadding),
          _buildFormLabel(AppStrings.phone, isMandatory: true),
          const SizedBox(height: 8),
          _phoneNumTextFormField(
            isRegisterTab ? phoneController : phoneControllerInvite,
          ),
          const SizedBox(height: defaultPadding),
          _buildFormLabel(AppStrings.email, isMandatory: true),
          const SizedBox(height: 8),
          _emailTextFormField(
            isRegisterTab ? emailController : emailControllerInvite,
          ),
          const SizedBox(height: defaultPadding),
          if (isRegisterTab) ..._buildRegisterFields(true),
        ],
      );
    } else {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildFormLabel(AppStrings.firstName, isMandatory: true),
                    const SizedBox(height: 8),
                    _buildTextFormField(
                      firstNameController,
                      AppStrings.enterFirstName,
                      isMandatory: true,
                    ),
                  ],
                ),
              ),
              const SizedBox(width: defaultPadding),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildFormLabel(AppStrings.lastName, isMandatory: true),
                    const SizedBox(height: 8),
                    _buildTextFormField(
                      lastNameController,
                      AppStrings.enterLastName,
                      isMandatory: true,
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: defaultPadding),

          _buildFormLabel(AppStrings.phone, isMandatory: true),
          const SizedBox(height: 8),
          _phoneNumTextFormField(
            isRegisterTab ? phoneController : phoneControllerInvite,
          ),
          const SizedBox(height: defaultPadding),

          _buildFormLabel(AppStrings.email, isMandatory: true),
          const SizedBox(height: 8),
          _emailTextFormField(
            isRegisterTab ? emailController : emailControllerInvite,
          ),
          const SizedBox(height: defaultPadding),

          if (isRegisterTab) ..._buildRegisterFields(false),
        ],
      );
    }
  }

  List<Widget> _buildRegisterFields(bool isMobile) {
    if (isMobile) {
      return [
        _buildFormLabel(AppStrings.city, isMandatory: true),
        const SizedBox(height: 8),
        _buildTextFormField(
          cityController,
          '',
          validator: (value) => InputValidators.validateTextLengthRange(
            value,
            fieldLabel: AppStrings.city,
          ),
          isMandatory: true,
        ),
        const SizedBox(height: defaultPadding),

        _buildFormLabel(AppStrings.stateProvince, isMandatory: true),
        const SizedBox(height: 8),
        _buildTextFormField(
          stateController,
          '',
          validator: (value) => InputValidators.validateTextLengthRange(
            value,
            fieldLabel: AppStrings.stateProvince,
          ),
          isMandatory: true,
        ),
        const SizedBox(height: defaultPadding),

        _buildFormLabel(AppStrings.postalZipCode, isMandatory: true),
        const SizedBox(height: 8),
        _buildTextFormField(
          postalCodeController,
          AppStrings.postalCodeEg,
          validator: (value) => InputValidators.validateZipCode(value),
          isMandatory: true,
        ),
        const SizedBox(height: defaultPadding),

        _buildFormLabel(AppStrings.country, isMandatory: true),
        const SizedBox(height: 8),
        _buildTextFormField(
          countryController,
          '',
          validator: (value) => InputValidators.validateTextLengthRange(
            value,
            fieldLabel: AppStrings.country,
          ),
          isMandatory: true,
        ),
        const SizedBox(height: defaultPadding),

        _buildFormLabel(AppStrings.agentLicenseId, isMandatory: true),
        const SizedBox(height: 8),
        _buildTextFormField(
          agentLicenseIdController,
          AppStrings.enterAgentLicenseId,
          isMandatory: true,
        ),
        const SizedBox(height: defaultPadding),

        _buildFormLabel(AppStrings.uploadAgentLicenseId, isMandatory: true),
        const SizedBox(height: 8),
        _buildUploadColumn(),
        const SizedBox(height: defaultPadding),

        _buildFormLabel(AppStrings.additionalInformation),
        const SizedBox(height: 8),
        _buildTextFormField(additionalInfoController, '', maxLines: 4),
      ];
    } else {
      return [
        _textFormFieldRowWidget(
          AppStrings.city,
          cityController,
          AppStrings.stateProvince,
          stateController,
          validatorValueLeftController: (value) =>
              InputValidators.validateTextLengthRange(
                value,
                fieldLabel: AppStrings.city,
              ),
          validatorValueRightController: (value) =>
              InputValidators.validateTextLengthRange(
                value,
                fieldLabel: AppStrings.stateProvince,
              ),
        ),
        const SizedBox(height: defaultPadding),

        _textFormFieldRowWidget(
          AppStrings.postalZipCode,
          postalCodeController,
          AppStrings.country,
          countryController,
          validatorValueLeftController: InputValidators.validateZipCode,
          validatorValueRightController: (value) =>
              InputValidators.validateTextLengthRange(
                value,
                fieldLabel: AppStrings.country,
              ),
        ),
        const SizedBox(height: defaultPadding),

        _buildFormLabel(AppStrings.agentLicenseId, isMandatory: true),
        const SizedBox(height: 8),
        _buildTextFormField(
          agentLicenseIdController,
          AppStrings.enterAgentLicenseId,
          isMandatory: true,
        ),
        const SizedBox(height: defaultPadding),

        _buildFormLabel(AppStrings.uploadAgentLicenseId, isMandatory: true),
        const SizedBox(height: 8),
        _buildUploadColumn(),
        const SizedBox(height: defaultPadding),

        _buildFormLabel(AppStrings.additionalInformation),
        const SizedBox(height: 8),
        _buildTextFormField(additionalInfoController, '', maxLines: 4),
      ];
    }
  }

  Column _buildUploadColumn() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildUploadField(
          AppStrings.chooseFileOrDragDrop,
          AppStrings.pdfOrImageOnly,
          agentLicenseFile,
          APIConsts.allowedFileExtensions,
        ),
        _fileUploadTxt(),
      ],
    );
  }

  ValueListenableBuilder<bool> _fileUploadTxt() {
    return ValueListenableBuilder<bool>(
      valueListenable: showFileUploadError,
      builder: (context, hasError, child) {
        if (hasError) {
          return Padding(
            padding: const EdgeInsets.only(top: 8.0, left: 16.0),
            child: Text(
              AppStrings.pleaseUploadLicence,
              style: AppFonts.regularTextStyle(12, color: Colors.red),
            ),
          );
        }
        return const SizedBox.shrink();
      },
    );
  }

  Widget _textFormFieldRowWidget(
    String leftLabel,
    TextEditingController leftController,
    String rightLabel,
    TextEditingController rightController, {
    String? Function(String?)? validatorValueLeftController,
    String? Function(String?)? validatorValueRightController,
  }) {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildFormLabel(leftLabel, isMandatory: true),
              const SizedBox(height: 8),
              _buildTextFormField(
                leftController,
                (leftController == postalCodeController) ? postalCodeEg : '',
                validator: validatorValueLeftController,
                isMandatory: true,
              ),
            ],
          ),
        ),
        const SizedBox(width: defaultPadding),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildFormLabel(rightLabel, isMandatory: true),
              const SizedBox(height: 8),
              _buildTextFormField(
                rightController,
                '',
                validator: validatorValueRightController,
                isMandatory: true,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _actionButtons(BuildContext context) {
    final isSmallMobile = Responsive.isSmallMobile(context);

    if (isSmallMobile) {
      return Column(
        children: [
          SizedBox(
            width: double.infinity,
            child: AppButton(
              label: AppStrings.register,
              backgroundColor: AppTheme.roundIconColor,
              foregroundColor: Colors.white,
              borderRadius: 25,
              padding: EdgeInsets.symmetric(
                horizontal: defaultPadding * 2,
                vertical: defaultPadding * 0.75,
              ),
              onPressed: () async => await _submitForm(context),
            ),
          ),
          const SizedBox(height: defaultPadding),
          SizedBox(
            width: double.infinity,
            child: AppButton(
              label: AppStrings.clear,
              backgroundColor: AppTheme.scaffoldBgColor,
              foregroundColor: AppTheme.primaryTextColor,
              borderRadius: 25,
              padding: EdgeInsets.symmetric(
                horizontal: defaultPadding * 2.5,
                vertical: defaultPadding * 0.75,
              ),
              onPressed: () => _showClearDataAlert(context),
            ),
          ),
        ],
      );
    } else {
      // Keep horizontal layout for regular mobile, tablet, and desktop
      return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          AppButton(
            label: AppStrings.clear,
            backgroundColor: AppTheme.scaffoldBgColor,
            foregroundColor: AppTheme.primaryTextColor,
            borderRadius: 25,
            padding: const EdgeInsets.symmetric(
              horizontal: defaultPadding * 2.5,
              vertical: defaultPadding / 2,
            ),
            onPressed: () => _showClearDataAlert(context),
          ),
          const SizedBox(width: defaultPadding),
          AppButton(
            label: AppStrings.register,
            backgroundColor: AppTheme.roundIconColor,
            foregroundColor: Colors.white,
            borderRadius: 25,
            padding: const EdgeInsets.symmetric(
              horizontal: defaultPadding * 2,
              vertical: defaultPadding / 2,
            ),
            onPressed: () async => await _submitForm(context),
          ),
        ],
      );
    }
  }

  Widget _buildFormLabel(String label, {bool isMandatory = false}) {
    return Align(
      alignment: Alignment.centerLeft,
      child: RichText(
        text: TextSpan(
          text: label,
          style: AppFonts.regularTextStyle(
            14,
            color: AppTheme.primaryTextColor,
          ),
          children: isMandatory
              ? [
                  TextSpan(
                    text: ' *',
                    style: AppFonts.regularTextStyle(
                      14,
                      color: AppTheme.textFieldMandatoryColor,
                    ),
                  ),
                ]
              : [],
        ),
      ),
    );
  }

  Widget _buildTextFormField(
    TextEditingController controller,
    String hintText, {
    bool isMandatory = false,
    String? Function(String?)? validator,
    TextInputType? keyboardType,
    List<TextInputFormatter>? inputFormatters,
    int maxLines = 1,
  }) {
    return AppTextField(
      controller: controller,
      hintText: hintText,
      isMandatory: isMandatory,
      validator: validator,
      keyboardType: keyboardType,
      inputFormatters: inputFormatters,
      isMobile: false,
    );
  }

  Widget _emailTextFormField(TextEditingController controller) {
    return _buildTextFormField(
      controller,
      AppStrings.enterEmail,
      isMandatory: true,
      keyboardType: TextInputType.emailAddress,
      validator: (value) => InputValidators.validateEmail(value),
    );
  }

  Widget _phoneNumTextFormField(TextEditingController controller) {
    return _buildTextFormField(
      controller,
      AppStrings.enterPhone,
      isMandatory: true,
      keyboardType: TextInputType.phone,
      inputFormatters: [
        FilteringTextInputFormatter.digitsOnly,
        LengthLimitingTextInputFormatter(10),
      ],
      validator: (value) => InputValidators.validatePhone(value),
    );
  }

  Widget _buildUploadField(
    String hintText,
    String formatText,
    ValueNotifier<PlatformFile?> fileNotifier,
    List<String> allowedExtensions,
  ) {
    return ValueListenableBuilder<bool>(
      valueListenable: showFileUploadError,
      builder: (context, hasError, child) {
        return ValueListenableBuilder<PlatformFile?>(
          valueListenable: fileNotifier,
          builder: (context, file, child) {
            final isSmallMobile = Responsive.isSmallMobile(context);

            // Determine border color based on validation state
            Color borderColor = Colors.grey;
            if (file != null) {
              borderColor = Colors.green.shade200;
            } else if (hasError) {
              borderColor = Colors.red;
            }

            return buildDottedBorderContainerWithRadius(
              borderRadius: 25.0,
              borderColor: borderColor,
              child: Container(
                width: double.infinity,
                height: isSmallMobile ? 100 : 120,
                padding: EdgeInsets.all(
                  isSmallMobile ? defaultPadding / 2 : defaultPadding,
                ),
                decoration: BoxDecoration(
                  color: file != null
                      ? Colors.green.shade50
                      : hasError
                      ? Colors.red.shade50
                      : AppTheme.docUploadBgColor,
                  borderRadius: BorderRadius.circular(25),
                  border: file != null
                      ? Border.all(color: Colors.green.shade200)
                      : hasError
                      ? Border.all(color: Colors.red.shade200)
                      : null,
                ),
                child: file != null
                    ? Stack(
                        children: [
                          Center(
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.check_circle,
                                  color: Colors.green,
                                  size: isSmallMobile ? 16 : 20,
                                ),
                                SizedBox(width: isSmallMobile ? 8 : 12),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Text(
                                        file.name,
                                        style: AppFonts.mediumTextStyle(
                                          isSmallMobile ? 12 : 14,
                                          color: Colors.green.shade700,
                                        ),
                                        overflow: TextOverflow.ellipsis,
                                        textAlign: TextAlign.center,
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        '${(file.size / 1024).toStringAsFixed(1)} ${AppStrings.fileSizeKB}',
                                        style: AppFonts.regularTextStyle(
                                          isSmallMobile ? 10 : 12,
                                          color: Colors.green.shade600,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Positioned(
                            top: 0,
                            right: 0,
                            child: GestureDetector(
                              onTap: () {
                                fileNotifier.value = null;
                                // Reset validation error when file is removed
                                showFileUploadError.value = false;
                              },
                              child: Icon(
                                Icons.close,
                                color: Colors.red,
                                size: isSmallMobile ? 16 : 20,
                              ),
                            ),
                          ),
                        ],
                      )
                    : Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          ElevatedButton.icon(
                            onPressed: () => _showFilePickerOptions(
                              context,
                              fileNotifier,
                              allowedExtensions,
                            ),
                            icon: Image.asset(
                              '$iconAssetpath/upload.png',
                              height: isSmallMobile ? 14 : 16,
                              width: isSmallMobile ? 14 : 16,
                            ),
                            label: Text(
                              AppStrings.upload,
                              style: AppFonts.mediumTextStyle(
                                isSmallMobile ? 12 : 14,
                                color: AppTheme.black,
                              ),
                            ),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.white,
                              foregroundColor: AppTheme.primaryTextColor,
                              elevation: 0,
                              padding: EdgeInsets.symmetric(
                                horizontal: isSmallMobile ? 8 : 12,
                                vertical: isSmallMobile ? 4 : 8,
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                                side: BorderSide(color: AppTheme.borderColor),
                              ),
                            ),
                          ),
                          SizedBox(height: isSmallMobile ? 4 : 8),
                          Text(
                            hintText,
                            textAlign: TextAlign.center,
                            style: AppFonts.mediumTextStyle(
                              isSmallMobile ? 10 : 12,
                              color: AppTheme.black,
                            ),
                          ),
                          Text(
                            formatText,
                            textAlign: TextAlign.center,
                            style: AppFonts.regularTextStyle(
                              isSmallMobile ? 9 : 12,
                              color: AppTheme.ternaryTextColor,
                            ),
                          ),
                        ],
                      ),
              ),
            );
          },
        );
      },
    );
  }

  /// Show file picker options for iOS compatibility
  Future<void> _showFilePickerOptions(
    BuildContext context,
    ValueNotifier<PlatformFile?> fileNotifier,
    List<String> allowedExtensions,
  ) async {
    if (kIsWeb) {
      // On web, directly use file picker
      return _pickFile(context, fileNotifier, allowedExtensions);
    }

    // On mobile, show options
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return SafeArea(
          child: Wrap(
            children: [
              ListTile(
                leading: const Icon(Icons.photo_library),
                title: const Text(AppStrings.photoLibrary),
                onTap: () {
                  Navigator.pop(context);
                  _pickFromGallery(context, fileNotifier);
                },
              ),
              ListTile(
                leading: const Icon(Icons.camera_alt),
                title: const Text(AppStrings.camera),
                onTap: () {
                  Navigator.pop(context);
                  _pickFromCamera(context, fileNotifier);
                },
              ),
              ListTile(
                leading: const Icon(Icons.folder),
                title: const Text(AppStrings.files),
                onTap: () {
                  Navigator.pop(context);
                  _pickFile(context, fileNotifier, allowedExtensions);
                },
              ),
              ListTile(
                leading: const Icon(Icons.cancel),
                title: const Text(AppStrings.cancel),
                onTap: () => Navigator.pop(context),
              ),
            ],
          ),
        );
      },
    );
  }

  /// Pick image from gallery using image_picker
  Future<void> _pickFromGallery(
    BuildContext context,
    ValueNotifier<PlatformFile?> fileNotifier,
  ) async {
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 80,
      );

      if (image != null) {
        // Convert XFile to PlatformFile
        final file = File(image.path);
        final bytes = await file.readAsBytes();

        final platformFile = PlatformFile(
          name: image.name,
          size: bytes.length,
          path: image.path,
          bytes: bytes,
        );

        fileNotifier.value = platformFile;
        showFileUploadError.value = false;
      }
    } catch (e) {
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text(
            '${AppStrings.failedToPickImageFromGallery}: ${e.toString()}',
          ),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// Pick image from camera using image_picker
  Future<void> _pickFromCamera(
    BuildContext context,
    ValueNotifier<PlatformFile?> fileNotifier,
  ) async {
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.camera,
        imageQuality: 80,
      );

      if (image != null) {
        // Convert XFile to PlatformFile
        final file = File(image.path);
        final bytes = await file.readAsBytes();

        final platformFile = PlatformFile(
          name: image.name,
          size: bytes.length,
          path: image.path,
          bytes: bytes,
        );

        fileNotifier.value = platformFile;
        showFileUploadError.value = false;
      }
    } catch (e) {
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text('${AppStrings.failedToCaptureImage}: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _pickFile(
    BuildContext context,
    ValueNotifier<PlatformFile?> fileNotifier,
    List<String> allowedExtensions,
  ) async {
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    try {
      FilePickerResult? result;
      if (kIsWeb) {
        // Web-specific configuration
        result = await FilePicker.platform.pickFiles(
          type: FileType.custom,
          allowedExtensions: allowedExtensions,
          allowMultiple: false,
          withData: true,
        );
      } else {
        // Mobile/Desktop configuration - try different approaches for iOS
        try {
          result = await FilePicker.platform.pickFiles(
            type: FileType.custom,
            allowedExtensions: allowedExtensions,
            allowMultiple: false,
            withData: false,
          );
        } catch (e) {
          debugPrint('Custom file type failed, trying FileType.any: $e');
          // Fallback to any file type if custom fails on iOS
          result = await FilePicker.platform.pickFiles(
            type: FileType.any,
            allowMultiple: false,
            withData: false,
            compressionQuality: 80,
          );
        }
      }

      debugPrint('File picker result: ${result?.files.length ?? 0} files');

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;
        debugPrint(
          'Selected file: ${file.name}, size: ${file.size}, extension: ${file.extension}',
        );

        // Validate file type for mobile (since we use FileType.any)
        if (!kIsWeb) {
          final extension = file.extension?.toLowerCase();
          if (extension == null || !allowedExtensions.contains(extension)) {
            debugPrint(
              'Invalid file type: $extension. Allowed: $allowedExtensions',
            );
            scaffoldMessenger.showSnackBar(
              SnackBar(
                content: Text(
                  '${AppStrings.pleaseSelectValidFileType} ${allowedExtensions.join(', ')}',
                ),
              ),
            );
            return;
          }
        }

        // Validate file based on platform
        if (kIsWeb) {
          if (file.bytes != null) {
            fileNotifier.value = file;
            // Clear validation error when file is selected
            showFileUploadError.value = false;
          } else {
            debugPrint('Web: File bytes not available');
          }
        } else {
          if (file.path != null) {
            fileNotifier.value = file;
            // Clear validation error when file is selected
            showFileUploadError.value = false;
          } else {
            debugPrint('Mobile: File path not available');
          }
        }
      } else {
        debugPrint('No file selected or result is null');
      }
    } catch (e) {
      debugPrint('$errorPickingFile: $e');
      // Show user-friendly error message
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text('$failedToOpenFilePicker: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _submitForm(BuildContext context) async {
    if (selectedIndex.value == 1) {
      //invite tab
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('Under development')));
      return;
    }
    final user = context.read<UserCubit>().state;
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    final agentCubit = context.read<AgentCubit>();

    // 1️⃣ Validate form
    if (!_formKey.currentState!.validate()) {
      scaffoldMessenger.showSnackBar(
        const SnackBar(content: Text(AppStrings.pleaseFillRequiredFields)),
      );
      return;
    }

    // 2️⃣ Check license file
    if (agentLicenseFile.value == null) {
      showFileUploadError.value = true;
      scaffoldMessenger.showSnackBar(
        const SnackBar(content: Text(AppStrings.pleaseUploadLicence)),
      );
      return;
    }
    showFileUploadError.value = false;

    // 3️⃣ Build payload
    final payload = _buildAgentPayload(user);

    // 4️⃣ Register agent
    await agentCubit.resgiterAgent(payload);
    final state = agentCubit.state;

    if (state is AgentCreated) {
      await _handleFileUpload(
        agentCubit: agentCubit,
        scaffoldMessenger: scaffoldMessenger,
        file: agentLicenseFile.value,
        userId: state.userId,
      );
    } else if (state is AgentLoaded) {
      scaffoldMessenger.showSnackBar(
        const SnackBar(
          backgroundColor: Colors.green,
          content: Text(AppStrings.agentCreatedSuccessfully),
        ),
      );
    } else if (state is AgentError) {
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text('${AppStrings.failedToCreateAgent}: ${state.message}'),
        ),
      );
    }
  }

  Map<String, dynamic> _buildAgentPayload(UserState user) {
    return {
      "recruiterId": user.user?.userId.trim(),
      "firstName": firstNameController.text.trim(),
      "lastName": lastNameController.text.trim(),
      "email": emailController.text.trim(),
      "phone": phoneController.text,
      "city": cityController.text.trim(),
      "state": stateController.text.trim(),
      "postalCode": postalCodeController.text.trim(),
      "country": countryController.text.trim(),
      "agentLicenseId": agentLicenseIdController.text.trim(),
      "additionalInfo": additionalInfoController.text.trim(),
    };
  }

  Future<void> _handleFileUpload({
    required AgentCubit agentCubit,
    required ScaffoldMessengerState scaffoldMessenger,
    required PlatformFile? file,
    required String? userId,
  }) async {
    if (file == null) {
      scaffoldMessenger.showSnackBar(
        SnackBar(
          backgroundColor: Colors.green,
          content: Text(AppStrings.agentCreatedSuccessfullyWithUploadPrompt),
        ),
      );
      return;
    }

    final isValidFile = kIsWeb ? file.bytes != null : file.path != null;
    if (!isValidFile) {
      scaffoldMessenger.showSnackBar(
        const SnackBar(content: Text(AppStrings.invalidFile)),
      );
      return;
    }

    final uploadFilePayload = {
      "userId": userId,
      "categoryType": APIConsts.agentCategoryType,
      "documentType": APIConsts.agentDocType,
      "file": file,
    };

    await agentCubit.uploadAgentFile(uploadFilePayload);
    final uploadState = agentCubit.state;

    if (uploadState is AgentFileUploaded) {
      scaffoldMessenger.showSnackBar(
        SnackBar(content: Text(AppStrings.agentCreatedSuccessfully)),
      );
      _clearForm();
    } else if (uploadState is AgentError) {
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text(
            '${AppStrings.fileUploadFailed}: ${uploadState.message}',
          ),
        ),
      );
    }
  }

  _showClearDataAlert(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text(AppStrings.clearData),
          content: Text(
            AppStrings.clearDataConfirmation,
            textAlign: TextAlign.center,
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: Text(AppStrings.cancel),
            ),
            TextButton(
              onPressed: () {
                _clearForm();
                Navigator.pop(context);
              },
              child: Text(AppStrings.ok),
            ),
          ],
        );
      },
    );
  }

  _clearForm() {
    firstNameController.clear();
    lastNameController.clear();
    phoneController.clear();
    emailController.clear();
    cityController.clear();
    stateController.clear();
    postalCodeController.clear();
    countryController.clear();
    agentLicenseIdController.clear();
    additionalInfoController.clear();
    agentLicenseFile.value = null;
    _formKey.currentState?.reset();
  }
}
