// import 'package:flutter/material.dart';
// import 'package:flutter_hooks/flutter_hooks.dart';
// import 'agents_list_screen.dart';

// class AgentsScreen extends HookWidget {
//   const AgentsScreen({Key? key}) : super(key: key);

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(body: Safe<PERSON>rea(child: const AgentsListScreen(onNavigateToAgentNetworkAgent: (AgentModel ) {  },)));
//   }
// }
