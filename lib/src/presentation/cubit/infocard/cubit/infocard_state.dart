import 'package:flutter/material.dart';
import 'package:neorevv/src/domain/models/info_card.dart';
import '/src/domain/models/info_card_item_model.dart';

@immutable
sealed class DashboardCubitState {}

final class DashboardInitial extends DashboardCubitState {}

final class DashboardCardsLoading extends DashboardCubitState {}

final class DashboardCardsSuccess extends DashboardCubitState {
  final List<InfoCardData> infocards;
  DashboardCardsSuccess(this.infocards);
}

final class DashboardCardsFailure extends DashboardCubitState {
  final String error;
  DashboardCardsFailure(this.error);
}
