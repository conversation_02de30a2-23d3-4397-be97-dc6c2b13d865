part of 'agent_cubit.dart';

@immutable
sealed class AgentState {}

final class AgentInitial extends AgentState {}

final class AgentLoading extends AgentState {}

final class AgentLoaded extends AgentState {
  final List<AgentModel> agents;
  final int totalCount;
  final int totalPages;
  final int currentPage;
  final bool hasMore;

  AgentLoaded({
    required this.agents,
    required this.totalCount,
    required this.currentPage,
    required this.totalPages,
    this.hasMore = false,
  });
}


final class AgentError extends AgentState {
  final String message;
  final int? statusCode;

  AgentError({required this.message, this.statusCode});
}

final class AgentCreated extends AgentState {
  final String? userId;
  final Map<String, dynamic>? responseData;

  AgentCreated({this.userId, this.responseData});
}

final class AgentFileUploaded extends AgentState {}
