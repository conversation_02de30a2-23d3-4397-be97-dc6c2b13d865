import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';
import '../../../core/config/app_strings.dart';
import '../../../core/services/exceptions.dart';
import '../../../domain/repository/broker_register_repository.dart';

part 'broker_register_state.dart';

class BrokerRegisterCubit extends Cubit<BrokerRegisterState> {
  BrokerRegisterCubit(this._brokerRegisterRepository)
    : super(BrokerRegisterInitial());
  final BrokerRegisterRepository _brokerRegisterRepository;

  Future<void> registerBrokerWithFiles({
    required Map<String, dynamic> brokerPayload,
    required List<FileUploadData> files,
  }) async {
    emit(BrokerRegisterLoading(true));
    try {
      final userId = await _brokerRegisterRepository.registerBroker(
        brokerPayload,
      );
      if (userId != null && userId.isNotEmpty) {
        if (files.isNotEmpty) {
          await _brokerRegisterRepository.registerBrokerWithFiles(
            brokerPayload: brokerPayload,
            files: files,
            userId: userId,
          );
          emit(BrokerRegisterSuccess());
        } else {
          emit(BrokerRegisterSuccessWithoutFiles());
        }
      } else {
        emit(BrokerRegisterError(error: failedToRegisterBroker));
      }
    } on ApiException catch (e) {
      emit(BrokerRegisterError(error: e.message));
    } catch (e) {
      emit(BrokerRegisterError(error: '$unexpectedError: ${e.toString()}'));
    }
  }
}
