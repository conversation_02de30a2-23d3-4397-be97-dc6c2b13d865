part of 'broker_register_cubit.dart';

@immutable
sealed class BrokerRegisterState {}

final class BrokerRegisterInitial extends BrokerReg<PERSON>State {}

final class BrokerRegisterLoading extends BrokerRegisterState {
  final bool isLoading;
  BrokerRegisterLoading(this.isLoading);
  @override
  List<Object> get props => [isLoading];
}

final class BrokerRegisterSuc<PERSON> extends BrokerRegisterState {
  BrokerRegisterSuccess();
}

final class BrokerRegisterError extends BrokerRegisterState {
  final String error;
  BrokerRegisterError({required this.error});
}

final class BrokerRegisterSuccessWithoutFiles extends BrokerRegisterState {
  BrokerRegisterSuccessWithoutFiles();
}
