part of 'sales_details_cubit.dart';

abstract class SalesDetailsState {}

final class SalesDetailsInitial extends SalesDetailsState {}

final class SalesDetailsLoading extends SalesDetailsState {}

final class SalesDetailsLoaded extends SalesDetailsState {
  final SalesDetailsApi? salesDetailsApi;

  SalesDetailsLoaded({required this.salesDetailsApi});
}

final class SalesDetailsError extends SalesDetailsState {
  final String message;
  final int? statusCode;

  SalesDetailsError({required this.message, this.statusCode});

  List<Object?> get props => [message, statusCode];
}
