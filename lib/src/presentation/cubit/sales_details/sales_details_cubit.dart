import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';

import '../../../core/services/exceptions.dart';
import '../../../domain/models/sales.dart';
import '../../../domain/repository/sales_details_repository.dart';

part 'sales_details_state.dart';

class SalesDetailsCubit extends Cubit<SalesDetailsState> {
  SalesDetailsCubit(this._salesDetailsRepository)
    : super(SalesDetailsInitial());
  final SalesDetailsRepository _salesDetailsRepository;

  Future<void> fetchSalesDetails(Map<String, dynamic> payload) async {
    emit(SalesDetailsLoading());

    try {
      final salesDetails = await _salesDetailsRepository.getSalesDetails(payload);

      emit(SalesDetailsLoaded(salesDetailsApi: salesDetails));
    } on ApiException catch (e) {
      emit(SalesDetailsError(message: e.message.toString(), statusCode: e.statusCode));
    } catch (e) {
      emit(
        SalesDetailsError(
          message: 'An unexpected error occurred: ${e.toString()}',
        ),
      );
    }
  }
}
