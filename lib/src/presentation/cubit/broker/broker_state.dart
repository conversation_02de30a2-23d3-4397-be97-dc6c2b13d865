part of 'broker_cubit.dart';

@immutable
sealed class BrokerState {}

final class BrokerInitial extends BrokerState {}

final class BrokerLoading extends BrokerState {}

final class BrokerLoaded extends BrokerState {
  final BrokerApi? brokerApi;
  BrokerLoaded({required this.brokerApi});
}

final class BrokerError extends BrokerState {
  final String message;
  final int? statusCode;
  BrokerError({required this.message, this.statusCode});

  List<Object?> get props => [message, statusCode];
}
