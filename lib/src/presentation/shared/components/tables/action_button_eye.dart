import 'package:flutter/material.dart';

import '../../../../core/config/constants.dart';
import '../../../../core/theme/app_fonts.dart';
import '../../../../core/theme/app_theme.dart';

class ActionButtonEye extends StatelessWidget {
  final VoidCallback onPressed;
  final bool isCompact;
  final bool isMobile;
  final String icon;
  final double padding;

  const ActionButtonEye({
    super.key,
    required this.onPressed,
    this.isCompact = false,
    this.isMobile = false,
    this.icon = commonIconEye,
    this.padding = 0.0,
  });

  @override
  Widget build(BuildContext context) {
    if (isMobile) {
      return _buildMobileButton();
    } else {
      return _buildDesktopButton();
    }
  }

  Widget _buildMobileButton() {
    return SizedBox(
      width: double.infinity,
      height: 40,
      child: ElevatedButton.icon(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppTheme.roundIconColor,
          foregroundColor: Colors.white,
          elevation: 0,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        ),
        icon: Image.asset(
          icon ?? commonIconEye,
          width: 16,
          height: 16,
          color: Colors.white,
        ),
        label: Text(
          'View',
          style: AppFonts.mediumTextStyle(14, color: Colors.white),
        ),
      ),
    );
  }

  Widget _buildDesktopButton() {
    final double buttonSize = isCompact
        ? 28
        : 28; // Fixed to 28x28 as per design
    final double iconSize = isCompact ? 18 : 18; // Fixed to 16x16 as per design

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(buttonSize / 2),
        child: Container(
          width: buttonSize,
          height: buttonSize,
          margin: const EdgeInsets.symmetric(horizontal: 2.5),
          padding: EdgeInsets.all(padding),
          decoration: BoxDecoration(
            color: AppTheme.roundIconBgColor,
            shape: BoxShape.circle,
          ),
          child: Center(
            child: Image.asset(
              icon ?? commonIconEye,
              width: iconSize,
              height: iconSize,
              color: AppTheme.roundIconColor,
            ),
          ),
        ),
      ),
    );
  }
}
