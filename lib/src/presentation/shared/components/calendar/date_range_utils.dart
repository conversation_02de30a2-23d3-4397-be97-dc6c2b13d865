import 'package:intl/intl.dart';

class DateRangeUtils {
  static final DateFormat _formatter = DateFormat('dd/MM/yyyy');

  /// Formats a date range for display
  static String formatDateRangeForDisplay(DateTime? start, DateTime? end) {
    if (start == null && end == null) {
      return 'Select date range';
    }

    if (start != null && end != null) {
      if (start.isAtSameMomentAs(end)) {
        return _formatter.format(start);
      }
      return '${_formatter.format(start)} - ${_formatter.format(end)}';
    } else if (start != null) {
      return _formatter.format(start);
    } else if (end != null) {
      return _formatter.format(end);
    }

    return 'Select date range';
  }

  /// Gets date range for quick selection options
  static Map<String, DateTime?> getQuickSelectionRange(String option) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    switch (option) {
      case 'Today':
        return {'start': today, 'end': today};

      case 'Yesterday':
        final yesterday = today.subtract(const Duration(days: 1));
        return {'start': yesterday, 'end': yesterday};

      case 'This Week':
        final startOfWeek = today.subtract(Duration(days: today.weekday - 1));
        final endOfWeek = startOfWeek.add(const Duration(days: 6));
        return {'start': startOfWeek, 'end': endOfWeek};

      case 'Last Week':
        final startOfLastWeek = today.subtract(
          Duration(days: today.weekday + 6),
        );
        final endOfLastWeek = startOfLastWeek.add(const Duration(days: 6));
        return {'start': startOfLastWeek, 'end': endOfLastWeek};

      case 'This Month':
        final startOfMonth = DateTime(today.year, today.month, 1);
        final endOfMonth = DateTime(today.year, today.month + 1, 0);
        return {'start': startOfMonth, 'end': endOfMonth};

      case 'Last Month':
        final startOfLastMonth = DateTime(today.year, today.month - 1, 1);
        final endOfLastMonth = DateTime(today.year, today.month, 0);
        return {'start': startOfLastMonth, 'end': endOfLastMonth};

      case 'This Quarter':
        final quarterStart = _getQuarterStart(today);
        final quarterEnd = _getQuarterEnd(today);
        return {'start': quarterStart, 'end': quarterEnd};

      case 'Last Quarter':
        final lastQuarterStart = _getLastQuarterStart(today);
        final lastQuarterEnd = _getLastQuarterEnd(today);
        return {'start': lastQuarterStart, 'end': lastQuarterEnd};

      case 'This Year':
        final startOfYear = DateTime(today.year, 1, 1);
        final endOfYear = DateTime(today.year, 12, 31);
        return {'start': startOfYear, 'end': endOfYear};

      case 'Last Year':
        final startOfLastYear = DateTime(today.year - 1, 1, 1);
        final endOfLastYear = DateTime(today.year - 1, 12, 31);
        return {'start': startOfLastYear, 'end': endOfLastYear};

      case 'Custom Range':
        return {'start': null, 'end': null};

      default:
        return {'start': null, 'end': null};
    }
  }

  /// Checks if a date is within a given range
  static bool isDateInRange(DateTime date, DateTime? start, DateTime? end) {
    if (start == null || end == null) return false;

    final dateOnly = DateTime(date.year, date.month, date.day);
    final startOnly = DateTime(start.year, start.month, start.day);
    final endOnly = DateTime(end.year, end.month, end.day);

    return dateOnly.isAfter(startOnly.subtract(const Duration(days: 1))) &&
        dateOnly.isBefore(endOnly.add(const Duration(days: 1)));
  }

  /// Checks if a date is the start of a range
  static bool isRangeStart(DateTime date, DateTime? start) {
    if (start == null) return false;

    return date.year == start.year &&
        date.month == start.month &&
        date.day == start.day;
  }

  /// Checks if a date is the end of a range
  static bool isRangeEnd(DateTime date, DateTime? end) {
    if (end == null) return false;

    return date.year == end.year &&
        date.month == end.month &&
        date.day == end.day;
  }

  // Private helper methods for quarter calculations
  static DateTime _getQuarterStart(DateTime date) {
    final quarter = ((date.month - 1) ~/ 3) + 1;
    final startMonth = (quarter - 1) * 3 + 1;
    return DateTime(date.year, startMonth, 1);
  }

  static DateTime _getQuarterEnd(DateTime date) {
    final quarter = ((date.month - 1) ~/ 3) + 1;
    final endMonth = quarter * 3;
    return DateTime(date.year, endMonth + 1, 0);
  }

  static DateTime _getLastQuarterStart(DateTime date) {
    final currentQuarter = ((date.month - 1) ~/ 3) + 1;
    if (currentQuarter == 1) {
      // Last quarter of previous year
      return DateTime(date.year - 1, 10, 1);
    } else {
      final lastQuarter = currentQuarter - 1;
      final startMonth = (lastQuarter - 1) * 3 + 1;
      return DateTime(date.year, startMonth, 1);
    }
  }

  static DateTime _getLastQuarterEnd(DateTime date) {
    final currentQuarter = ((date.month - 1) ~/ 3) + 1;
    if (currentQuarter == 1) {
      // Last quarter of previous year
      return DateTime(date.year - 1, 12, 31);
    } else {
      final lastQuarter = currentQuarter - 1;
      final endMonth = lastQuarter * 3;
      return DateTime(date.year, endMonth + 1, 0);
    }
  }

  /// Formats a single date
  static String formatDate(DateTime date) {
    return _formatter.format(date);
  }

  /// Parses a date string
  static DateTime? parseDate(String dateString) {
    try {
      return _formatter.parse(dateString);
    } catch (e) {
      return null;
    }
  }

  /// Gets the number of days between two dates
  static int daysBetween(DateTime start, DateTime end) {
    return end.difference(start).inDays;
  }

  /// Checks if two dates are the same day
  static bool isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  /// Gets the start of the day (00:00:00)
  static DateTime startOfDay(DateTime date) {
    return DateTime(date.year, date.month, date.day);
  }

  /// Gets the end of the day (23:59:59)
  static DateTime endOfDay(DateTime date) {
    return DateTime(date.year, date.month, date.day, 23, 59, 59);
  }
}
