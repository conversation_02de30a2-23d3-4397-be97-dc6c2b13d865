import 'package:flutter/foundation.dart';
import 'package:universal_html/html.dart' as html;

/// Service for managing cookies across different platforms
class CookieManager {
  /// Clears all cookies for the current domain
  /// Only works on web platform, no-op on other platforms
  static Future<void> clearAllCookies() async {
    if (kIsWeb) {
      try {
        // Get all cookies for the current domain
        final cookies = html.document.cookie;

        if (cookies != null && cookies.isNotEmpty) {
          // Split cookies by semicolon and clear each one
          final cookieList = cookies.split(';');

          for (final cookie in cookieList) {
            final cookieName = cookie.split('=')[0].trim();
            if (cookieName.isNotEmpty) {
              // Set cookie to expire in the past to delete it
              html.document.cookie =
                  '$cookieName=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';

              // Also try to clear with domain specification
              final domain = html.window.location.hostname;
              if (domain != null && domain.isNotEmpty) {
                html.document.cookie =
                    '$cookieName=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=$domain;';
                html.document.cookie =
                    '$cookieName=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.$domain;';
              }
            }
          }
        }

        if (kDebugMode) {
          print('CookieManager: All cookies cleared successfully');
        }
      } catch (e) {
        if (kDebugMode) {
          print('CookieManager: Error clearing cookies: $e');
        }
      }
    } else {
      // No-op for non-web platforms
      if (kDebugMode) {
        print('CookieManager: Cookie clearing not supported on this platform');
      }
    }
  }

  /// Clears specific cookies by name
  /// Only works on web platform, no-op on other platforms
  static Future<void> clearCookiesByName(List<String> cookieNames) async {
    if (kIsWeb) {
      try {
        for (final cookieName in cookieNames) {
          if (cookieName.isNotEmpty) {
            // Set cookie to expire in the past to delete it
            html.document.cookie =
                '$cookieName=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';

            // Also try to clear with domain specification
            final domain = html.window.location.hostname;
            if (domain != null && domain.isNotEmpty) {
              html.document.cookie =
                  '$cookieName=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=$domain;';
              html.document.cookie =
                  '$cookieName=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.$domain;';
            }
          }
        }

        if (kDebugMode) {
          print(
            'CookieManager: Specific cookies cleared: ${cookieNames.join(', ')}',
          );
        }
      } catch (e) {
        if (kDebugMode) {
          print('CookieManager: Error clearing specific cookies: $e');
        }
      }
    } else {
      // No-op for non-web platforms
      if (kDebugMode) {
        print('CookieManager: Cookie clearing not supported on this platform');
      }
    }
  }

  /// Gets all cookies as a map (for debugging purposes)
  /// Only works on web platform, returns empty map on other platforms
  static Map<String, String> getAllCookies() {
    if (kIsWeb) {
      try {
        final cookies = html.document.cookie;
        final cookieMap = <String, String>{};

        if (cookies != null && cookies.isNotEmpty) {
          final cookieList = cookies.split(';');

          for (final cookie in cookieList) {
            final parts = cookie.split('=');
            if (parts.length >= 2) {
              final name = parts[0].trim();
              final value = parts.sublist(1).join('=').trim();
              cookieMap[name] = value;
            }
          }
        }

        return cookieMap;
      } catch (e) {
        if (kDebugMode) {
          print('CookieManager: Error getting cookies: $e');
        }
        return {};
      }
    } else {
      return {};
    }
  }
}
