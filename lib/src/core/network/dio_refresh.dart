// dio_refresh_token_client.dart
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:neorevv/src/core/network/api_config.dart';

final String refreshBaseUrl = APIConfig.baseUrl;

class DioRefreshTokenClient {
  static Dio createInstance() {
    final dio = Dio(
      BaseOptions(
        baseUrl: refreshBaseUrl,
        headers: {
          'accept': 'application/json',
          'Content-Type': 'application/json',
        },
        connectTimeout: const Duration(seconds: 10),
        receiveTimeout: const Duration(seconds: 10),
      ),
    );

    if (kIsWeb) {
      // Ensure cookies are included automatically on web
      dio.interceptors.add(
        InterceptorsWrapper(
          onRequest: (options, handler) {
            options.extra['withCredentials'] = true;
            handler.next(options);
          },
        ),
      );
    }

    return dio;
  }
}
