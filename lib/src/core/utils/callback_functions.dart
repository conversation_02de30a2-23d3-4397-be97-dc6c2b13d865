typedef BooleanCallback = void Function(bool value);
typedef VoidCallback = void Function();
typedef StringCallback = void Function(String value);
typedef DateTimeCallback = void Function(DateTime? value);
typedef IntCallback = void Function(int value);
typedef DateFilterMultipleCallback =
    Function(Map<String, DateTime?> selectedDates);
typedef AllFiltersCallback = Function(Map<String, dynamic> allFilters);
