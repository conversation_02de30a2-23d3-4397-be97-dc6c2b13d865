import '../config/app_strings.dart';
import 'regex.dart';

class InputValidators {
  InputValidators._();

  static String? validateEmail(String? value) {
    if (value == null || value.isEmpty) return emailIsRequired;
    final emailRegex = RegExUtils.emailRegex;
    if (!emailRegex.hasMatch(value)) return invalidEmail;
    return null;
  }

  static String? validatePhone(String? value) {
    if (value == null || value.isEmpty) {
      return phoneNumberIsRequired;
    }
    // US phone validation: 10 digits (excluding country code)
    // Allow formats like: 1234567890, ************
    final cleanedNumber = value.replaceAll(RegExUtils.nonNumeric, '');
    if (cleanedNumber.length != 10) {
      return invalidPhone;
    }
    if (value.length != 10) {
      return phoneNumberExceed;
    }
    return null;
  }

  static String? validatePassword(String? value) {
    if (value == null || value.isEmpty) return passwordIsRequired;
    if (value.length < 6) return passwordMustBeAtLeast6Characters;
    return null;
  }

  static String? validateConfirmPassword(String? value, String password) {
    if (value != password) return passwordsDoNotMatch;
    return null;
  }

  static String? validateRequiredField(String? value) {
    if (value == null || value.isEmpty) return thisFieldIsRequired;
    return null;
  }

  static String? validateZipCode(String? value) {
    if (value == null || value.trim().isEmpty) return postalCodeIsRequired;
    final zipPattern = RegExp(r'^[0-9]{5}-[0-9]{4}$');
    if (!zipPattern.hasMatch(value.trim())) {
      return 'Invalid zip code format. eg: 72546-4567';
    }
    return null;
  }

  static String? validateTextLengthRange(
    String? value, {
    String fieldLabel = 'Name',
  }) {
    if (value == null || value.trim().isEmpty) return '$fieldLabel is required';
    final trimmed = value.trim();
    if (trimmed.length < 2 || trimmed.length > 100) {
      return '$fieldLabel length must be 2-100';
    }
    return null;
  }
}
