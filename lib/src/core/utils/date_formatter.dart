import 'package:intl/intl.dart';

class AppDateFormatter {
  AppDateFormatter._();


  static String formatDateMMddyyyy(DateTime? date) {
    if (date == null) {
      return '';
    }
    return DateFormat('MM-dd-yyyy').format(date);
  }

  static String formatDateyyyyMMdd(DateTime date) {
    return DateFormat('yyyy-MM-dd').format(date);
  }

  static DateTime? parseStringToDateMMddyyyy(String dateString) {
    try {
      return DateFormat('MM-dd-yyyy').parse(dateString);
    } catch (e) {
      return null;
    }
  }

  //DateFormat('MMMM yyyy').
  static String formatCalendarHeader(DateTime date) {
    return DateFormat('MMMM yyyy').format(date);
  }

  static DateTime? formatMonthYearJson(String? date) {
    if (date == null || date.isEmpty) {
      return null;
    }
    return DateFormat("MM-dd-yyyy").parse(date);
  }

  //DateFormat('yyyy-MM').format
  static String formatMonthYear(DateTime date) {
    return DateFormat('yyyy-MM').format(date);
  }
}
