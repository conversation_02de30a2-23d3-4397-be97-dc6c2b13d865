// Custom Dotted Line Widget
import 'dart:math' as math;

import 'package:flutter/material.dart';

import '../theme/app_theme.dart';

class DottedLine extends StatelessWidget {
  final double height;
  final double width;
  final Color color;
  final double dashWidth;
  final double dashSpace;
  final Axis direction;

  const DottedLine({
    Key? key,
    this.height = 1,
    this.width = double.infinity,
    this.color = Colors.grey,
    this.dashWidth = 5,
    this.dashSpace = 3,
    this.direction = Axis.horizontal,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      size: Size(width, height),
      painter: DottedLinePainter(
        color: color,
        dashWidth: dashWidth,
        dashSpace: dashSpace,
        direction: direction,
      ),
    );
  }
}

// Custom Painter for Dotted Line
class DottedLinePainter extends CustomPainter {
  final Color color;
  final double dashWidth;
  final double dashSpace;
  final Axis direction;

  DottedLinePainter({
    required this.color,
    required this.dashWidth,
    required this.dashSpace,
    required this.direction,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = direction == Axis.horizontal ? size.height : size.width
      ..style = PaintingStyle.stroke;

    double totalLength = direction == Axis.horizontal
        ? size.width
        : size.height;
    double currentPosition = 0;

    while (currentPosition < totalLength) {
      if (direction == Axis.horizontal) {
        canvas.drawLine(
          Offset(currentPosition, size.height / 2),
          Offset(
            (currentPosition + dashWidth).clamp(0, size.width),
            size.height / 2,
          ),
          paint,
        );
      } else {
        canvas.drawLine(
          Offset(size.width / 2, currentPosition),
          Offset(
            size.width / 2,
            (currentPosition + dashWidth).clamp(0, size.height),
          ),
          paint,
        );
      }
      currentPosition += dashWidth + dashSpace;
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

/// Custom painter for dotted border with optional curved corners
class DottedBorderPainter extends CustomPainter {
  final double borderRadius;
  final Color borderColor;
  final double borderWidth;
  final double dashWidth;
  final double dashSpace;
  final bool curvedCorners;

  DottedBorderPainter({
    required this.borderRadius,
    required this.borderColor,
    required this.borderWidth,
    required this.dashWidth,
    required this.dashSpace,
    this.curvedCorners = true,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = borderColor
      ..strokeWidth = borderWidth
      ..style = PaintingStyle.stroke;

    final rect = Offset.zero & size;
    final rrect = RRect.fromRectAndRadius(rect, Radius.circular(borderRadius));

    if (curvedCorners && borderRadius > 0) {
      _drawDottedRRect(canvas, rrect, paint);
    } else {
      _drawDottedRect(canvas, rect, paint);
    }
  }

  void _drawDottedRect(Canvas canvas, Rect rect, Paint paint) {
    // Top
    _drawDottedLine(
      canvas,
      paint,
      Offset(rect.left, rect.top),
      Offset(rect.right, rect.top),
    );
    // Right
    _drawDottedLine(
      canvas,
      paint,
      Offset(rect.right, rect.top),
      Offset(rect.right, rect.bottom),
    );
    // Bottom
    _drawDottedLine(
      canvas,
      paint,
      Offset(rect.right, rect.bottom),
      Offset(rect.left, rect.bottom),
    );
    // Left
    _drawDottedLine(
      canvas,
      paint,
      Offset(rect.left, rect.bottom),
      Offset(rect.left, rect.top),
    );
  }

  void _drawDottedRRect(Canvas canvas, RRect rrect, Paint paint) {
    final path = Path()..addRRect(rrect);
    final metrics = path.computeMetrics().first;
    final length = metrics.length;
    double distance = 0.0;
    while (distance < length) {
      final next = math.min(dashWidth, length - distance);
      final extractPath = metrics.extractPath(distance, distance + next);
      canvas.drawPath(extractPath, paint);
      distance += dashWidth + dashSpace;
    }
  }

  void _drawDottedLine(Canvas canvas, Paint paint, Offset start, Offset end) {
    final totalLength = (end - start).distance;
    final direction = (end - start) / totalLength;
    double drawn = 0.0;
    while (drawn < totalLength) {
      final currentStart = start + direction * drawn;
      final segmentLength = math.min(dashWidth, totalLength - drawn);
      final currentEnd = currentStart + direction * segmentLength;
      canvas.drawLine(currentStart, currentEnd, paint);
      drawn += dashWidth + dashSpace;
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

// Simple usage examples
Widget buildForwardarrow() {
  return Container(width: 40, child: Icon(Icons.chevron_right));
}

Widget buildVerticalDottedLine() {
  return const DottedLine(
    height: 100,
    width: 1,
    color: Colors.grey,
    dashWidth: 5,
    dashSpace: 3,
    direction: Axis.vertical,
  );
}

// Advanced usage with different styles
Widget buildCustomDottedLines() {
  return Column(
    children: [
      // Thin horizontal dotted line
      const DottedLine(
        height: 1,
        color: Colors.grey,
        dashWidth: 3,
        dashSpace: 2,
      ),

      const SizedBox(height: 20),

      // Thick horizontal dashed line
      const DottedLine(
        height: 2,
        color: Colors.blue,
        dashWidth: 8,
        dashSpace: 4,
      ),

      const SizedBox(height: 20),

      // Row with vertical dotted line
      Row(
        children: [
          const Expanded(child: Text("Left Content")),
          const DottedLine(
            height: 50,
            width: 1,
            color: Colors.orange,
            dashWidth: 4,
            dashSpace: 3,
            direction: Axis.vertical,
          ),
          const SizedBox(width: 10),
          const Expanded(child: Text("Right Content")),
        ],
      ),

      const SizedBox(height: 20),

      // Dotted border container
      Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.transparent),
        ),
        child: Column(
          children: [
            const Text("Content with dotted border"),
            const SizedBox(height: 10),
            // Top border
            const DottedLine(
              height: 1,
              color: Colors.grey,
              dashWidth: 4,
              dashSpace: 2,
            ),
            const SizedBox(height: 10),
            const Text("More content here"),
          ],
        ),
      ),
    ],
  );
}

// Container with dotted border on all sides, now supports curved (rounded) corners
Widget buildDottedBorderContainerWithRadius({
  required Widget child,
  double borderRadius = 10,
  Color borderColor = Colors.grey,
  double borderWidth = 1,
  double dashWidth = 5,
  double dashSpace = 3,
  EdgeInsets padding = const EdgeInsets.all(0),
  bool curvedCorners = true, // new parameter
}) {
  return CustomPaint(
    painter: DottedBorderPainter(
      borderRadius: borderRadius,
      borderColor: borderColor,
      borderWidth: borderWidth,
      dashWidth: dashWidth,
      dashSpace: dashSpace,
      curvedCorners: curvedCorners,
    ),
    child: Container(padding: padding, child: child),
  );
}

// Container with dotted border on all sides
Widget buildDottedBorderContainer({
  required Widget child,
  double borderRadius = 10,
  Color borderColor = Colors.grey,
  double borderWidth = 1,
  double dashWidth = 5,
  double dashSpace = 3,
  EdgeInsets padding = const EdgeInsets.all(16),
}) {
  return Container(
    decoration: BoxDecoration(
      borderRadius: BorderRadius.circular(borderRadius),
    ),
    padding: padding,
    child: Stack(
      children: [
        // Content
        child,

        // Top border
        Positioned(
          top: 0,
          left: 0,
          right: 0,
          child: DottedLine(
            height: borderWidth,
            color: borderColor,
            dashWidth: dashWidth,
            dashSpace: dashSpace,
          ),
        ),

        // Bottom border
        Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          child: DottedLine(
            height: borderWidth,
            color: borderColor,
            dashWidth: dashWidth,
            dashSpace: dashSpace,
          ),
        ),

        // Left border
        Positioned(
          top: 0,
          left: 0,
          bottom: 0,
          child: DottedLine(
            width: borderWidth,
            color: borderColor,
            dashWidth: dashWidth,
            dashSpace: dashSpace,
            direction: Axis.vertical,
          ),
        ),

        // Right border
        Positioned(
          top: 0,
          right: 0,
          bottom: 0,
          child: DottedLine(
            width: borderWidth,
            color: borderColor,
            dashWidth: dashWidth,
            dashSpace: dashSpace,
            direction: Axis.vertical,
          ),
        ),
      ],
    ),
  );
}

// Usage example in a card
Widget buildCardWithDottedSeparator() {
  return Card(
    child: Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            "Card Title",
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          const DottedLine(
            height: 1,
            color: Colors.grey,
            dashWidth: 4,
            dashSpace: 2,
          ),
          const SizedBox(height: 12),
          const Text("Card content goes here..."),
          const SizedBox(height: 16),
          const DottedLine(
            height: 1,
            color: Colors.blue,
            dashWidth: 6,
            dashSpace: 3,
          ),
          const SizedBox(height: 12),
          const Text("More content after dotted line"),
        ],
      ),
    ),
  );
}
