enum UserRole {
  platformOwner,
  admin,
  brokerage,
  agent,
  officeStaff,
  receptionist,
  none,
}

// Helper to get display string
String userRoleToString(UserRole role) {
  switch (role) {
    case UserRole.platformOwner:
      return "b65032ee-e823-4540-acff-c1a4ba240342";
    case UserRole.admin:
      return "ab3c61c9-0dc1-4cc4-8004-5fae06ab48ea";
    case UserRole.brokerage:
      return "1ef3c304-16a5-4826-aa4c-b869995ace61";
    case UserRole.agent:
      return "74d1c93a-e5df-4da9-8e50-eb2e27eefa4e";
    case UserRole.officeStaff:
      return "OFFICE_STAFF";
    case UserRole.receptionist:
      return "RECEPTIONIST";
    case UserRole.none:
      return "NONE";
  }
}

UserRole stringToUserRole(String role) {
  switch (role) {
    case "b65032ee-e823-4540-acff-c1a4ba240342":
      return UserRole.platformOwner;
    case "ab3c61c9-0dc1-4cc4-8004-5fae06ab48ea":
      return UserRole.admin;
    case "1ef3c304-16a5-4826-aa4c-b869995ace61":
      return UserRole.brokerage;
    case "74d1c93a-e5df-4da9-8e50-eb2e27eefa4e":
      return UserRole.agent;
    case "OFFICE_STAFF":
      return UserRole.officeStaff;
    case "RECEPTIONIST":
      return UserRole.receptionist;
    case "NONE":
      return UserRole.none;
    default:
      return UserRole.none;
  }
}
