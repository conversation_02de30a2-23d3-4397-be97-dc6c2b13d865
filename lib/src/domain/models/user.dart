import '../../core/enum/user_role.dart';

class User {
  String firstName;
  String lastName;
  String userId;
  String roleId;
  String name;
  String roleName;
  UserRole role;
  String logoUrl;
  String avatarUrl;

  User({
    required this.firstName,
    required this.lastName,
    required this.userId,
    required this.roleId,
    required this.name,
    required this.role,
    required this.roleName,
    required this.logoUrl,
    required this.avatarUrl,
  });

  User copyWith({
    String? firstName,
    String? lastName,
    String? name,
    UserRole? role,
    String? logoUrl,
    String? avatarUrl,
    String? roleName,
    String? id,
    String? userRoleId,
  }) => User(
    firstName: firstName ?? this.firstName,
    lastName: lastName ?? this.lastName,
    name: name ?? this.name,
    role: role ?? this.role,
    logoUrl: logoUrl ?? this.logoUrl,
    avatarUrl: avatarUrl ?? this.avatarUrl,
    roleName: roleName ?? this.roleName,
    userId: id ?? this.userId,
    roleId: userRoleId ?? this.roleId,
  );

  factory User.fromJson(Map<String, dynamic> json) => User(
    firstName: json["firstName"],
    lastName: json["lastName"],
    name: json["name"],
    role: stringToUserRole(json["userRoleId"] ?? ''),
    logoUrl: json["logoUrl"] ?? '',
    avatarUrl: json["avatarUrl"] ?? '',
    roleName: json["roleDisplayName"],
    userId: json["id"] ?? "",
    roleId: json["userRoleId"] ?? "",
  );

  Map<String, dynamic> toJson() => {
    "firstName": firstName,
    "lastName": lastName,
    "name": name,
    "userRoleId": userRoleToString(role),
    "logoUrl": logoUrl,
    "avatarUrl": avatarUrl,
    "roleDisplayName": roleName,
    "id": userId,
  };
}
