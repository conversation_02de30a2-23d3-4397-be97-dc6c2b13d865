// To parse this JSON data, do
//
//     final agentFilterList = agentFilterListFrom<PERSON>son(jsonString);

import 'dart:convert';

List<FilterModel> agentFilterListFromJson(String str) => List<FilterModel>.from(
  json.decode(str).map((x) => FilterModel.fromJson(x)),
);

String agentFilterListToJson(List<FilterModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class FilterModel {
  String id;
  dynamic key;
  String value;

  FilterModel({required this.id, required this.key, required this.value});

  FilterModel copyWith({String? id, dynamic key, String? value}) => FilterModel(
    id: id ?? this.id,
    key: key ?? this.key,
    value: value ?? this.value,
  );

  factory FilterModel.fromJson(Map<String, dynamic> json) =>
      FilterModel(id: json["id"], key: json["key"], value: json["value"]);

  Map<String, dynamic> toJson() => {"id": id, "key": key, "value": value};
}
