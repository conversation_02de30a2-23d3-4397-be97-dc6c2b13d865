class TopPerformers {
  String id;
  String name;
  int downlineAgentsCount;
  double monthlyRevenue;
  int monthlySalesCount;
  String associatedBroker;
  int rank;

  TopPerformers({
    required this.id,
    required this.name,
    required this.downlineAgentsCount,
    required this.monthlyRevenue,
    required this.monthlySalesCount,
    required this.associatedBroker,
    required this.rank,
  });

  factory TopPerformers.fromJson(Map<String, dynamic> json) {
    return TopPerformers(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      downlineAgentsCount: json['downline_agents_count'] ?? 0,
      monthlyRevenue: (json['monthly_revenue'] as num?)?.toDouble() ?? 0.0,
      monthlySalesCount: json['monthly_sales_count'] ?? 0,
      associatedBroker: json['associated_brokerage'] ?? '',
      rank: json['rank'] ?? 0,
    );
  }

  TopPerformers copyWith({
    String? id,
    String? name,
    int? downlineAgentsCount,
    double? monthlyRevenue,
    int? monthlySalesCount,
    String? associatedBroker,
    int? rank,
  }) => TopPerformers(
    id: id ?? this.id,
    name: name ?? this.name,
    downlineAgentsCount: downlineAgentsCount ?? this.downlineAgentsCount,
    monthlyRevenue: monthlyRevenue ?? this.monthlyRevenue,
    monthlySalesCount: monthlySalesCount ?? this.monthlySalesCount,
    associatedBroker: associatedBroker ?? this.associatedBroker,
    rank: rank ?? this.rank,
  );
}
