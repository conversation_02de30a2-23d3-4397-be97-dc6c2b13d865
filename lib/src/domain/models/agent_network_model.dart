// To parse this JSON data, do
//
//     final agentNetworkModel = agentNetworkModelFromJson(jsonString);

import 'dart:convert';

AgentNetworkModel agentNetworkModelFromJson(String str) =>
    AgentNetworkModel.fromJson(json.decode(str));

String agentNetworkModelToJson(AgentNetworkModel data) =>
    json.encode(data.toJson());

class AgentNetworkModel {
  UserProfile userProfile;
  List<DirectRecruit> directRecruits;
  int totalDirectRecruits;

  AgentNetworkModel({
    required this.userProfile,
    required this.directRecruits,
    required this.totalDirectRecruits,
  });

  factory AgentNetworkModel.fromJson(Map<String, dynamic> json) =>
      AgentNetworkModel(
        userProfile: UserProfile.fromJson(json["userProfile"]),
        directRecruits: List<DirectRecruit>.from(
          json["directRecruits"].map((x) => DirectRecruit.fromJson(x)),
        ),
        totalDirectRecruits: json["totalDirectRecruits"],
      );

  Map<String, dynamic> toJson() => {
    "userProfile": userProfile.toJson(),
    "directRecruits": List<dynamic>.from(directRecruits.map((x) => x.toJson())),
    "totalDirectRecruits": totalDirectRecruits,
  };
}

class DirectRecruit {
  String userId;
  String name;
  String email;
  String role;
  int recruitsCount;

  DirectRecruit({
    required this.userId,
    required this.name,
    required this.email,
    required this.role,
    required this.recruitsCount,
  });

  factory DirectRecruit.fromJson(Map<String, dynamic> json) => DirectRecruit(
    userId: json["userId"],
    name: json["name"],
    email: json["email"],
    role: json["role"],
    recruitsCount: json["recruitsCount"] ?? 0,
  );

  Map<String, dynamic> toJson() => {
    "userId": userId,
    "name": name,
    "email": email,
    "role": role,
    "recruitsCount": recruitsCount,
  };
}

class UserProfile {
  String userId;
  String name;
  String email;
  String phone;
  String role;
  int totalSalesRevenue;
  int totalCommissionEarned;
  int recruitsCount;
  String? profileImageUrl;
  bool depthLimitReached;

  UserProfile({
    required this.userId,
    required this.name,
    required this.email,
    required this.phone,
    required this.role,
    required this.totalSalesRevenue,
    required this.totalCommissionEarned,
    required this.recruitsCount,
    required this.profileImageUrl,
    required this.depthLimitReached,
  });

  factory UserProfile.fromJson(Map<String, dynamic> json) => UserProfile(
    userId: json["userId"],
    name: json["name"],
    email: json["email"],
    phone: json["phone"],
    role: json["role"],
    totalSalesRevenue: json["totalSalesRevenue"] ?? 0,
    totalCommissionEarned: json["totalCommissionEarned"] ?? 0,
    recruitsCount: json["recruitsCount"] ?? 0,
    profileImageUrl: json["profileImageUrl"] ?? "",
    depthLimitReached: json["depthLimitReached"] ?? false,
  );

  Map<String, dynamic> toJson() => {
    "userId": userId,
    "name": name,
    "email": email,
    "phone": phone,
    "role": role,
    "totalSalesRevenue": totalSalesRevenue,
    "totalCommissionEarned": totalCommissionEarned,
    "recruitsCount": recruitsCount,
    "profileImageUrl": profileImageUrl,
    "depthLimitReached": depthLimitReached,
  };
}
