import 'dart:io';

import 'package:file_picker/file_picker.dart';

class FileUploadData {
  final String categoryType;
  final String documentType;
  final PlatformFile file;

  FileUploadData({
    required this.categoryType,
    required this.documentType,
    required this.file,
  });
}
abstract class BrokerRegisterRepository {
  Future<String?> registerBroker(Map<String, dynamic> payload);
   Future<void> uploadFile({
    required String userId,
    required String categoryType,
    required String documentType,
    required PlatformFile file,
  });
  Future<void> registerBrokerWithFiles({
    required Map<String, dynamic> brokerPayload,
    required List<FileUploadData> files,
    required String userId,
  });
}
