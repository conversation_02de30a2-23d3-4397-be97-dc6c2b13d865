import 'package:dio/dio.dart';
import 'package:neorevv/src/domain/models/agent_network_model.dart';
import 'package:neorevv/src/domain/repository/get_network_item_repository.dart';

import '../../core/network/dio_client.dart';
import '../../core/services/api_error_handler.dart';
import '../../core/services/exceptions.dart';

class GetNetworkItemRepositoryImpl extends GetNetworkItemRepository {
  GetNetworkItemRepositoryImpl();

  @override
  Future<AgentNetworkModel> getNetworkItem(String url, String userId) async {
    try {
      final dio = await DioClient.getDio();

      final response = await dio.get('$url/$userId');

      if (response.statusCode == 200) {
        return AgentNetworkModel.fromJson(response.data);
      } else {
        throw ApiErrorHandler.handleResponseError(
          response.statusCode,
          response.data,
        );
      }
    } on DioException catch (e) {
      throw ApiErrorHandler.handleDioException(
        e,
        "Failed to fetch network item",
      );
    } catch (e) {
      throw ApiException(message: e.toString(), statusCode: 500);
    }
  }
}
