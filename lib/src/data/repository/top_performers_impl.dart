import 'package:dio/dio.dart';

import '../../core/config/app_strings.dart';
import '../../core/network/api_config.dart';
import '../../core/network/dio_client.dart';
import '../../core/services/api_error_handler.dart';
import '../../core/services/exceptions.dart';
import '../../domain/models/top_performers.dart';
import '../../domain/repository/top_performers_repository.dart';

class TopPerformersRepositoryImpl extends TopPerformersRepository {
  TopPerformersRepositoryImpl();

  static String baseUrl = APIConfig.baseUrl;
  static const String brokerageTopPerformersUrl =
      APIConfig.brokerageTopPerformers;
  static const String agentTopPerformersUrl = APIConfig.agentTopPerformers;

  @override
  Future<List<TopPerformers>> getBrokerageTopPerformers(
    String monthFilter,
  ) async {
    try {
      final dio = await DioClient.getDio();
      final response = await dio.get(
        brokerageTopPerformersUrl,
        queryParameters: {'monthFilter': monthFilter},
      );

      if (response.statusCode == 200) {
        final List<dynamic> jsonList = response.data['data'];
        return jsonList.map((e) => TopPerformers.fromJson(e)).toList();
      } else {
        throw ApiErrorHandler.handleResponseError(
          response.statusCode,
          response.data,
        );
      }
    } on DioException catch (e) {
      throw ApiErrorHandler.handleDioException(
        e,
        failedToFetchBrokerageTopPerformers,
      );
    } catch (e) {
      throw ApiException(
        message: '$unexpectedError: ${e.toString()}',
        statusCode: 500,
      );
    }
  }

  @override
  Future<List<TopPerformers>> getAgentTopPerformers(String monthFilter) async {
    try {
      final dio = await DioClient.getDio();
      final response = await dio.get(
        agentTopPerformersUrl,
        queryParameters: {'monthFilter': monthFilter},
      );

      if (response.statusCode == 200) {
        final List<dynamic> jsonList = response.data['data'];
        return jsonList.map((e) => TopPerformers.fromJson(e)).toList();
      } else {
        throw ApiErrorHandler.handleResponseError(
          response.statusCode,
          response.data,
        );
      }
    } on DioException catch (e) {
      throw ApiErrorHandler.handleDioException(
        e,
        failedToFetchAgentTopPerformers,
      );
    } catch (e) {
      throw ApiException(
        message: '$unexpectedError: ${e.toString()}',
        statusCode: 500,
      );
    }
  }
}
