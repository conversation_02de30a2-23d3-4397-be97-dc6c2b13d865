import 'package:dio/dio.dart';

import '../../core/config/app_strings.dart';
import '../../core/network/api_config.dart';
import '../../core/network/dio_client.dart';
import '../../core/services/api_error_handler.dart';
import '../../core/services/exceptions.dart';
import '../../domain/models/sales.dart';
import '../../domain/repository/sales_details_repository.dart';

class SalesDetailsRepositoryImpl extends SalesDetailsRepository {
  SalesDetailsRepositoryImpl();

  static String baseUrl = APIConfig.baseUrl;
  static const String salesDetailsUrl = APIConfig.salesDetails;

  @override
  Future<SalesDetailsApi?> getSalesDetails(Map<String, dynamic> payload) async {
    try {
      final dio = await DioClient.getDio();
      final response = await dio.post(salesDetailsUrl, data: payload);

      if (response.statusCode == 200) {
        final Map<String, dynamic> dataList = response.data ?? {};
        final salesList = SalesDetailsApi.fromJson(dataList);
        return salesList;
        // return Sales.fromJson(response.data);
      } else {
        throw ApiErrorHandler.handleResponseError(
          response.statusCode,
          response.data,
        );
      }
    } on DioException catch (e) {
      throw ApiErrorHandler.handleDioException(e, failedToFetchSalesDetails);
    } catch (e) {
      throw ApiException(
        message: '$unexpectedError: ${e.toString()}',
        statusCode: 500,
      );
    }
  }
}
