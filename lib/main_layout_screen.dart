import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:neorevv/src/domain/models/agent_model.dart';
import '/src/domain/models/broker_api.dart';
import '/src/core/enum/user_role.dart';
import '/src/presentation/cubit/user/user_cubit.dart';
import '/src/presentation/cubit/auth/cubit/auth_cubit.dart';
import '/src/core/navigation/web_router.dart';
import '/src/presentation/shared/components/breadcrumb_navigation.dart';
import '/src/presentation/shared/components/header.dart';
import 'src/presentation/screens/agent/agent_registration_screen.dart';
import 'src/presentation/screens/agent/agents_list_screen.dart';
import '/src/presentation/screens/sales/sales_review_doc_screen.dart';
import '/src/presentation/screens/dashboard/components/mobile_drawer.dart';
import '/src/presentation/screens/agent_network/agent_network_screen.dart';
import 'src/core/config/app_strings.dart' as AppStrings;
import 'src/core/config/constants.dart';
import 'src/core/config/responsive.dart';
import 'src/core/config/tab_config.dart';
import 'src/core/theme/app_theme.dart';
import 'src/core/theme/app_fonts.dart';
import 'src/domain/models/user.dart';
import 'src/presentation/screens/broker/brokerage_list_screen.dart';
import 'src/presentation/screens/broker/register_broker_screen.dart';
import 'src/presentation/screens/dashboard/components/dashboard_content.dart';
import 'src/presentation/screens/dashboard/dashboard_screen.dart';
import 'src/presentation/screens/page_transition/in_app_page_transition.dart';
import 'src/presentation/screens/page_transition/staggered_fade_in.dart';
import 'src/presentation/screens/sales/sales_screen.dart';

class MainLayoutScreen extends HookWidget {
  final String? initialTab;

  const MainLayoutScreen({super.key, this.initialTab});

  @override
  Widget build(BuildContext context) {
    final user = context.watch<UserCubit>().state.user;

    final mainTabsCount = tabList(user?.role).length;

    final registerBrokerTabIndex = tabList(
      user?.role,
    ).indexWhere((tab) => tab == AppStrings.registerBroker);

    final registerAgentTabIndex = tabList(
      user?.role,
    ).indexWhere((tab) => tab == AppStrings.registerAgent);

    final agentNetworkTabIndex = tabList(
      user?.role,
    ).indexWhere((tab) => tab == AppStrings.agentNetwork);

    final salesDocIndex = tabList(
      user?.role,
    ).indexWhere((tab) => tab == AppStrings.salesDoc);

    // Get initial tab index based on URL
    final initialTabIndex = _getInitialTabIndex(user?.role);

    // State management
    final selectedTabIndex = useState<int>(initialTabIndex);
    final selectedBroker = useState<Brokers?>(null);
    final selectedAgent = useState<AgentModel?>(null);

    // Listen to URL changes and update selected tab accordingly
    useEffect(() {
      final currentTabIndex = _getInitialTabIndex(user?.role);
      if (currentTabIndex != selectedTabIndex.value) {
        // Use a post-frame callback to ensure onTabSelected is available
        WidgetsBinding.instance.addPostFrameCallback((_) {
          selectedTabIndex.value = currentTabIndex;
          // Clear selected broker/agent when navigating via browser back/forward
          if (currentTabIndex < mainTabsCount) {
            selectedBroker.value = null;
            selectedAgent.value = null;
          }
        });
      }
      return null;
    }, [initialTab, user?.role]);

    final isSmallMobile = useMemoized(
      () => Responsive.isSmallMobile(context),
      [],
    );

    // Navigation callbacks - memoized to prevent unnecessary rebuilds
    // final navigateToAgentNetwork = useCallback((Brokers broker) {
    //   // selectedAgent.value = null;
    //   selectedBroker.value = broker;
    //   selectedTabIndex.value = agentNetworkTabIndex;
    // }, []);

    final navigateToAgentNetwork = useCallback((Brokers broker) {
      selectedBroker.value = broker;
      selectedTabIndex.value = agentNetworkTabIndex;
      context.replace(AppRoutes.agentNetwork.path);
    }, [user]);

    final navigateToAgentNetworkAgent = useCallback((AgentModel agent) {
      // selectedBroker.value = null;
      selectedAgent.value = agent;
      selectedTabIndex.value = agentNetworkTabIndex;
      context.replace(AppRoutes.agentNetwork.path);
    }, [user]);

    final onTabSelected = useCallback((int index, {bool updateUrl = true}) {
      selectedTabIndex.value = index;
      // Clear selected broker when switching to main tabs
      if (index < mainTabsCount) {
        selectedBroker.value = null;
      }
      // Update URL based on selected tab only if not coming from URL change
      if (updateUrl) {
        _updateUrlForTab(context, index, user?.role);
      }
    }, [user]);

    // Wrapper for components that expect Function(int)
    final onTabSelectedWrapper = useCallback((int index) {
      onTabSelected(index, updateUrl: true);
    }, [onTabSelected]);

    final onAddNewPressed = useCallback((String navigationType) {
      if (navigationType == 'office_staff') {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('Under development')));
      } else {
        switch (navigationType) {
          case 'agent':
            selectedTabIndex.value = registerAgentTabIndex;
            context.replace(AppRoutes.registerAgent.path);
            break;
          case 'broker':
            selectedTabIndex.value = registerBrokerTabIndex;
            context.replace(AppRoutes.registerBroker.path);
            break;
        }
      }
    }, [registerAgentTabIndex, registerBrokerTabIndex]);

    final onAddNewPressedMobile = useCallback(() {
      if (user?.role == UserRole.admin) {
        selectedTabIndex.value = registerBrokerTabIndex;
        context.replace(AppRoutes.registerBroker.path);
      } else if (user?.role == UserRole.brokerage ||
          user?.role == UserRole.agent) {
        selectedTabIndex.value = registerAgentTabIndex;
        context.replace(AppRoutes.registerAgent.path);
      }
    }, [user]);

    final handleSaleSelection = useCallback(() {
      selectedTabIndex.value = salesDocIndex;
      context.replace(AppRoutes.saleReviewDoc.path);
    }, [user]);

    // Memoized tab configuration to prevent rebuilds
    final tabs = useMemoized(
      () => _buildTabConfigurations(
        navigateToAgentNetwork,
        navigateToAgentNetworkAgent,
        handleSaleSelection,
        selectedBroker,
        selectedAgent,
        user,
      ),
      [
        navigateToAgentNetwork,
        navigateToAgentNetworkAgent,
        selectedBroker.value,
        selectedAgent.value, // <-- add this
        handleSaleSelection,
        user,
      ],
    );

    return BlocListener<AuthCubit, AuthState>(
      listener: (context, state) {
        if (state is AuthLogout) {
          // Navigate to login page when logout state is emitted
          context.go(AppRoutes.login.path);
        }
      },
      child: Scaffold(
        backgroundColor: AppTheme.scaffoldBgColor,
        drawer: Responsive.showDrawer(context)
            ? MobileDrawer(
                user: user,
                selectedTab: selectedTabIndex.value,
                selectedTabIndex: selectedTabIndex.value,
                onTabSelected: onTabSelectedWrapper,

                tabs: tabs,
                onAddNewPressed: onAddNewPressedMobile,
              )
            : null,
        body: SafeArea(
          child: Column(
            children: [
              // Fixed Header at the top
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: isSmallMobile
                      ? mobileHorizontalPadding
                      : webLayoutmargin,
                ),
                child: Header(
                  tabs: tabs,
                  selectedTabIndex: selectedTabIndex.value,
                  user: user,
                  onAddNewPressed: onAddNewPressed,
                  onTabSelected: onTabSelectedWrapper,
                ),
              ),

              // Scrollable content below the header
              Expanded(
                child: _MainContent(
                  selectedTabIndex: selectedTabIndex,
                  selectedBroker: selectedBroker,
                  tabs: tabs,
                  onTabSelected: onTabSelectedWrapper,
                  isSmallMobile: isSmallMobile,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Tab configuration builder - seperated for better organization
  List<TabConfig> _buildTabConfigurations(
    Function(Brokers) navigateToAgentNetwork,
    Function(AgentModel) navigateToAgentNetworkAgent,
    Function() handleSaleSelection,
    ValueNotifier<Brokers?> selectedBroker,
    ValueNotifier<AgentModel?> selectedAgent,
    User? memoUser,
  ) {
    final userRole = memoUser?.role ?? UserRole.none;

    final dashboardTabConfig = TabConfig(
      title: AppStrings.dashboardTab,
      content: _buildDashboardContent(
        navigateToAgentNetwork,
        navigateToAgentNetworkAgent,
      ),
      icon: Icons.dashboard_outlined,
    );

    final brokersTabConfig = TabConfig(
      title: AppStrings.brokersTab,
      content: _buildBrokerageContent(navigateToAgentNetwork),
      icon: Icons.business_outlined,
    );

    final agentsTabConfig = TabConfig(
      title: AppStrings.agentsTab,
      content: AgentsListScreen(
        onNavigateToAgentNetworkAgent: navigateToAgentNetworkAgent,
      ),
      icon: Icons.people_outline,
    );

    final salesTabConfig = TabConfig(
      title: AppStrings.salesTab,
      content: SalesScreen(handleSaleSelection: () => handleSaleSelection()),
      icon: Icons.trending_up_outlined,
    );

    // final commissionTabConfig = TabConfig(
    //   title: AppStrings.commissionTab,
    //   content: const _PlaceholderContent(text: AppStrings.commissionContent),
    //   icon: Icons.account_balance_wallet_outlined,
    // );

    final reportsTabConfig = TabConfig(
      title: AppStrings.reportsTab,
      content: const _PlaceholderContent(text: AppStrings.reportsContent),
      icon: Icons.assessment_outlined,
    );

    final registerBrokerTabConfig = TabConfig(
      title: AppStrings.registerBroker,
      content: RegisterBrokerScreen(),
      icon: Icons.add,
      hidden: true,
    );

    final registerAgentTabConfig = TabConfig(
      title: AppStrings.registerAgent,
      content: AgentRegistrationScreen(),
      icon: Icons.add,
      hidden: true,
    );

    final agentNetworkTabConfig = TabConfig(
      title: AppStrings.agentNetwork,
      hideBreadcrumb: true,
      content: _buildAgentNetwork(selectedBroker, selectedAgent),
      icon: Icons.add,
      hidden: true,
    );

    final salesDocTabConfig = TabConfig(
      title: AppStrings.salesDoc,
      hideBreadcrumb: false,
      content: SalesReviewDocScreen(enableEditing: false),
      icon: Icons.add,
      hidden: true,
    );

    final roleTabMap = {
      UserRole.platformOwner: [
        dashboardTabConfig,
        brokersTabConfig,
        agentsTabConfig,
        salesTabConfig,
        reportsTabConfig,
        agentNetworkTabConfig,
        salesDocTabConfig,
      ],
      UserRole.admin: [
        dashboardTabConfig,
        brokersTabConfig,
        agentsTabConfig,
        salesTabConfig,
        registerBrokerTabConfig,
        agentNetworkTabConfig,
        salesDocTabConfig,
      ],
      UserRole.brokerage: [
        dashboardTabConfig,
        agentsTabConfig,
        salesTabConfig,
        reportsTabConfig,
        registerBrokerTabConfig,
        registerAgentTabConfig,
        agentNetworkTabConfig,
        salesDocTabConfig,
      ],
      UserRole.agent: [
        dashboardTabConfig,
        agentsTabConfig,
        salesTabConfig,
        reportsTabConfig,
        registerAgentTabConfig,
        agentNetworkTabConfig,
        salesDocTabConfig,
      ],
    };

    return roleTabMap[userRole] ??
        [dashboardTabConfig, salesTabConfig, reportsTabConfig];
  }

  // Helper method to get initial tab index based on URL
  int _getInitialTabIndex(UserRole? userRole) {
    if (initialTab == null) return 0;

    final tabs = tabList(userRole);

    switch (initialTab) {
      case 'dashboard':
        return tabs.indexWhere((tab) => tab == AppStrings.dashboardTab);
      case 'brokerages':
        return tabs.indexWhere((tab) => tab == AppStrings.brokersTab);
      case 'agents':
        return tabs.indexWhere((tab) => tab == AppStrings.agentsTab);
      case 'sales':
        return tabs.indexWhere((tab) => tab == AppStrings.salesTab);
      case 'reports':
        return tabs.indexWhere((tab) => tab == AppStrings.reportsTab);
      case 'register-brokerage':
        return tabs.indexWhere((tab) => tab == AppStrings.registerBroker);
      case 'register-agent':
        return tabs.indexWhere((tab) => tab == AppStrings.registerAgent);
      case 'agent-network':
        return tabs.indexWhere((tab) => tab == AppStrings.agentNetwork);
      case 'sale-review-doc':
        return tabs.indexWhere((tab) => tab == AppStrings.salesDoc);
      default:
        return 0;
    }
  }

  // Helper method to update URL based on selected tab
  void _updateUrlForTab(
    BuildContext context,
    int tabIndex,
    UserRole? userRole,
  ) {
    final tabs = tabList(userRole);
    if (tabIndex >= tabs.length) return;

    final tabTitle = tabs[tabIndex];
    String route = AppRoutes.dashboard.path;

    switch (tabTitle) {
      case AppStrings.dashboardTab:
        route = AppRoutes.dashboard.path;
        break;
      case AppStrings.brokersTab:
        route = AppRoutes.brokerages.path;
        break;
      case AppStrings.agentsTab:
        route = AppRoutes.agents.path;
        break;
      case AppStrings.salesTab:
        route = AppRoutes.sales.path;
        break;
      case AppStrings.reportsTab:
        route = AppRoutes.reports.path;
        break;
      case AppStrings.registerBroker:
        route = AppRoutes.registerBroker.path;
        break;
      case AppStrings.registerAgent:
        route = AppRoutes.registerAgent.path;
        break;
      case AppStrings.agentNetwork:
        route = AppRoutes.agentNetwork.path;
        break;
      case AppStrings.salesDoc:
        route = AppRoutes.saleReviewDoc.path;
        break;
    }

    // Use replace instead of go to update URL without rebuilding the widget
    context.replace(route);
  }

  DashboardScreen _buildDashboardContent(
    Function(Brokers) navigateToAgentNetwork,
    Function(AgentModel) navigateToAgentNetworkAgent,
  ) {
    return DashboardScreen(
      onNavigateToAgentNetwork: navigateToAgentNetwork,
      onNavigateToAgentNetworkAgent: navigateToAgentNetworkAgent,
    );
  }

  Widget _buildBrokerageContent(Function(Brokers) navigateToAgentNetwork) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // BrokersTable(onNavigateToAgentNetwork: navigateToAgentNetwork),
        BrokerageListScreen(onNavigateToAgentNetwork: navigateToAgentNetwork),

        const SizedBox(height: defaultPadding),
      ],
    );
  }

  Widget _buildAgentNetwork(
    ValueNotifier<Brokers?> selectedBroker,
    ValueNotifier<AgentModel?> selectedAgent,
  ) {
    if (selectedBroker.value != null) {
      return AgentNetworkScreen(
        selectedBrokerId: selectedBroker.value!.userId,
        showScaffold: false,
      );
    } else if (selectedAgent.value != null) {
      return AgentNetworkScreen(
        selectedBrokerId: selectedAgent.value!.userId,
        showScaffold: false,
      );
    }
    return Center(child: Text(AppStrings.noBrokerSelected));
  }
}

// Optimized content widgets - separated for better performance and maintainability
class _PlaceholderContent extends StatelessWidget {
  final String text;

  const _PlaceholderContent({required this.text});

  @override
  Widget build(BuildContext context) {
    return Center(child: Text(text, style: AppFonts.mediumTextStyle(16)));
  }
}

// Main content widget - extracted for better organization and performance
class _MainContent extends StatelessWidget {
  final ValueNotifier<int> selectedTabIndex;
  final ValueNotifier<Brokers?> selectedBroker;
  final List<TabConfig> tabs;
  final Function(int) onTabSelected;
  final bool isSmallMobile;

  const _MainContent({
    required this.selectedTabIndex,
    required this.selectedBroker,
    required this.tabs,
    required this.onTabSelected,
    required this.isSmallMobile,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return SingleChildScrollView(
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minHeight:
                  constraints.maxHeight -
                  MediaQuery.of(context).padding.top -
                  MediaQuery.of(context).padding.bottom,
            ),
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: isSmallMobile
                    ? mobileHorizontalPadding
                    : webLayoutmargin,
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Top content section
                  Column(
                    children: [
                      const SizedBox(height: defaultPadding),
                      //  if (!tabs[selectedTabIndex.value].hideBreadcrumb) ...[
                      if (selectedTabIndex.value < tabs.length &&
                          !tabs[selectedTabIndex.value].hideBreadcrumb) ...[
                        // Breadcrumb - only show when not on dashboard
                        _buildBreadcrumb(),
                      ],
                      // Dynamic Content Area with animation
                      _buildAnimatedContent(),
                    ],
                  ),

                  // Footer - will be pushed to bottom when content is short
                  // but will scroll naturally when content is long
                  const Footer(),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildBreadcrumb() {
    return ValueListenableBuilder<int>(
      valueListenable: selectedTabIndex,
      builder: (context, tabIndex, _) {
        //  if (tabIndex == 0) return const SizedBox.shrink();
        if (tabIndex == 0 || tabIndex >= tabs.length)
          return const SizedBox.shrink();

        return StaggeredFadeIn(
          delay: Duration.zero,
          child: Column(
            children: [
              BreadCrumbNavigation(
                hierarchyPath: _getBreadcrumbPath(
                  tabIndex,
                  selectedBroker.value,
                  tabs,
                ),
                onNavigate: _handleBreadcrumbNavigation,
              ),
              const SizedBox(height: defaultPadding),
            ],
          ),
        );
      },
    );
  }

  void _handleBreadcrumbNavigation(int navigationIndex) {
    onTabSelected(navigationIndex);
    // if (navigationIndex == 0) {
    //   // Go to dashboard
    //   selectedTabIndex.value = 0;
    //   selectedBroker.value = null;
    //   context.replace(AppRoutes.dashboard.path);
    //   // } else if (tabs[selectedTabIndex.value].title ==
    // } else if (selectedTabIndex.value < tabs.length &&
    //     tabs[selectedTabIndex.value].title == AppStrings.agentNetworkScreen &&
    //     navigationIndex == 1) {
    //   // Navigate back to Agents tab from Agent Network
    //   selectedTabIndex.value = 2; // Agents tab index
    //   selectedBroker.value = null;
    //   context.replace(AppRoutes.agents.path);
    // } else {

    // }
  }

  Widget _buildAnimatedContent() {
    return ValueListenableBuilder<int>(
      valueListenable: selectedTabIndex,
      builder: (context, tabIndex, _) {
        //
        if (tabIndex >= tabs.length) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            selectedTabIndex.value = 0;
          });

          return Container();
        }
        //
        return StaggeredFadeIn(
          delay: staggerDelay,
          child: InAppPageTransition(
            key: ValueKey(tabIndex),
            tabIndex: tabIndex,
            child: tabs[tabIndex].content,
          ),
        );
      },
    );
  }

  List<String> _getBreadcrumbPath(
    int tabIndex,
    Brokers? broker,
    List<TabConfig> tabs,
  ) {
    final tabTitle = tabs[tabIndex].title;

    if (tabTitle == AppStrings.agentNetworkScreen && broker != null) {
      return [
        AppStrings.dashboardAdmin,
        AppStrings.agentsTab,
        '${AppStrings.agentNetworkScreen} - ${broker.fullName}',
      ];
    } else if (tabTitle == AppStrings.registerBroker) {
      return [
        AppStrings.dashboardAdmin,
        AppStrings.brokersTab,
        AppStrings.registerBroker,
      ];
    } else if (tabTitle == AppStrings.salesDoc) {
      return [AppStrings.dashboardAdmin, AppStrings.sales, AppStrings.salesDoc];
    } else {
      return [AppStrings.dashboardAdmin, tabTitle];
    }
  }
}
